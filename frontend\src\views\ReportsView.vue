<template>
  <div class="reports-container">
    <!-- 顶部导航栏 -->
    <TopNavbar />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 页面标题 -->
      <section class="page-header">
        <h1 class="page-title">
          <span class="title-icon">📊</span>
          健康报告
        </h1>
        <p class="page-subtitle">全面了解您的健康状况和进展</p>
      </section>

      <!-- 时间范围选择器 -->
      <section class="period-selector-section">
        <div class="period-selector-card">
          <h2 class="section-title">报告时间范围</h2>
          <div class="period-buttons">
            <button
              v-for="period in periods"
              :key="period.value"
              @click="selectedPeriod = period.value"
              :class="['period-btn', { active: selectedPeriod === period.value }]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
      </section>

      <!-- 加载状态 -->
      <section v-if="isLoading" class="loading-section">
        <div class="loading-card">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在生成健康报告...</p>
        </div>
      </section>

      <!-- 错误状态 -->
      <section v-else-if="error" class="error-section">
        <div class="error-card">
          <div class="error-icon">⚠️</div>
          <h3 class="error-title">报告生成失败</h3>
          <p class="error-message">{{ error }}</p>
          <button @click="loadReport" class="retry-btn">重新生成</button>
        </div>
      </section>

      <!-- 报告内容 -->
      <div v-else-if="report" class="report-content">
        <!-- 综合健康评估卡片 -->
        <div class="assessment-overview">
          <div class="overview-card">
            <div class="card-header">
              <h2 class="card-title">
                <span class="card-icon">🎯</span>
                综合健康评估
              </h2>
              <div class="report-period">{{ getPeriodText(selectedPeriod) }}</div>
            </div>

            <div class="score-display">
              <div class="overall-score">
                <div class="score-circle" :class="getScoreClass(report.healthAssessment.overallScore)">
                  <span class="score-number">{{ report.healthAssessment.overallScore }}</span>
                  <span class="score-label">总分</span>
                </div>
              </div>

              <div class="score-breakdown">
                <div class="score-item">
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: report.healthAssessment.nutritionScore + '%' }"></div>
                  </div>
                  <div class="score-info">
                    <span class="score-name">营养评分</span>
                    <span class="score-value">{{ report.healthAssessment.nutritionScore }}</span>
                  </div>
                </div>

                <div class="score-item">
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: report.healthAssessment.goalAchievementScore + '%' }"></div>
                  </div>
                  <div class="score-info">
                    <span class="score-name">目标达成</span>
                    <span class="score-value">{{ report.healthAssessment.goalAchievementScore }}</span>
                  </div>
                </div>

                <div class="score-item">
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: report.healthAssessment.consistencyScore + '%' }"></div>
                  </div>
                  <div class="score-info">
                    <span class="score-name">规律性</span>
                    <span class="score-value">{{ report.healthAssessment.consistencyScore }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 趋势指示器 -->
            <div class="trend-indicators">
              <div class="trend-item" v-for="(trend, key) in report.healthAssessment.trends" :key="key">
                <span class="trend-icon" :class="trend">{{ getTrendIcon(trend) }}</span>
                <span class="trend-label">{{ getTrendLabel(key) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 报告详情网格 -->
        <div class="report-grid">
          <!-- 营养摄入报告 -->
          <div class="report-card nutrition-card">
            <div class="card-header">
              <h3 class="card-title">
                <span class="card-icon">🥗</span>
                营养摄入分析
              </h3>
            </div>

            <div class="card-content">
              <!-- 热量统计 -->
              <div class="nutrition-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ Math.round(report.nutrition.totalCalories) }}</div>
                  <div class="stat-label">总热量 (kcal)</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ Math.round(report.nutrition.averageCalories) }}</div>
                  <div class="stat-label">日均热量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ report.nutrition.complianceScore }}%</div>
                  <div class="stat-label">合规评分</div>
                </div>
              </div>

              <!-- 宏量营养素分布 -->
              <div class="macronutrient-chart">
                <h4 class="chart-title">宏量营养素分布</h4>
                <div class="macro-bars">
                  <div class="macro-item">
                    <div class="macro-bar">
                      <div class="macro-fill protein" :style="{ width: report.nutrition.macronutrientPercentages.protein + '%' }"></div>
                    </div>
                    <div class="macro-info">
                      <span class="macro-name">蛋白质</span>
                      <span class="macro-value">{{ Math.round(report.nutrition.macronutrientPercentages.protein) }}%</span>
                    </div>
                  </div>

                  <div class="macro-item">
                    <div class="macro-bar">
                      <div class="macro-fill fat" :style="{ width: report.nutrition.macronutrientPercentages.fat + '%' }"></div>
                    </div>
                    <div class="macro-info">
                      <span class="macro-name">脂肪</span>
                      <span class="macro-value">{{ Math.round(report.nutrition.macronutrientPercentages.fat) }}%</span>
                    </div>
                  </div>

                  <div class="macro-item">
                    <div class="macro-bar">
                      <div class="macro-fill carb" :style="{ width: report.nutrition.macronutrientPercentages.carbohydrate + '%' }"></div>
                    </div>
                    <div class="macro-info">
                      <span class="macro-name">碳水化合物</span>
                      <span class="macro-value">{{ Math.round(report.nutrition.macronutrientPercentages.carbohydrate) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 健康目标进度报告 -->
          <div class="report-card goals-card">
            <div class="card-header">
              <h3 class="card-title">
                <span class="card-icon">🎯</span>
                健康目标进度
              </h3>
            </div>

            <div class="card-content">
              <!-- 目标统计 -->
              <div class="goals-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ report.goalProgress.activeGoals }}</div>
                  <div class="stat-label">活跃目标</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ report.goalProgress.completedGoals }}</div>
                  <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ report.goalProgress.currentStreak }}</div>
                  <div class="stat-label">连续天数</div>
                </div>
              </div>

              <!-- 目标详情列表 -->
              <div class="goals-list">
                <div
                  v-for="goal in report.goalProgress.goalDetails.slice(0, 3)"
                  :key="goal.goalId"
                  class="goal-item"
                >
                  <div class="goal-info">
                    <div class="goal-name">{{ goal.goalName }}</div>
                    <div class="goal-progress">
                      <div class="progress-bar">
                        <div class="progress-fill" :style="{ width: goal.progress + '%' }"></div>
                      </div>
                      <span class="progress-text">{{ Math.round(goal.progress) }}%</span>
                    </div>
                  </div>
                  <div class="goal-trend" :class="goal.trend">
                    {{ getTrendIcon(goal.trend) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 膳食记录分析 -->
          <div class="report-card meals-card">
            <div class="card-header">
              <h3 class="card-title">
                <span class="card-icon">🍽️</span>
                膳食记录分析
              </h3>
            </div>

            <div class="card-content">
              <!-- 膳食统计 -->
              <div class="meals-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ report.mealAnalysis.totalMeals }}</div>
                  <div class="stat-label">总膳食数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ Math.round(report.mealAnalysis.averageMealsPerDay * 10) / 10 }}</div>
                  <div class="stat-label">日均膳食</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ report.mealAnalysis.eatingPatterns.regularityScore }}%</div>
                  <div class="stat-label">规律性评分</div>
                </div>
              </div>

              <!-- 膳食类型分布 -->
              <div class="meal-distribution">
                <h4 class="chart-title">膳食类型分布</h4>
                <div class="distribution-chart">
                  <div
                    v-for="(count, mealType) in report.mealAnalysis.mealTypeDistribution"
                    :key="mealType"
                    class="distribution-item"
                  >
                    <div class="distribution-bar">
                      <div
                        class="distribution-fill"
                        :class="mealType"
                        :style="{ width: (count / report.mealAnalysis.totalMeals * 100) + '%' }"
                      ></div>
                    </div>
                    <div class="distribution-info">
                      <span class="distribution-name">{{ getMealTypeName(mealType) }}</span>
                      <span class="distribution-value">{{ count }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 热门食物 -->
              <div class="popular-foods">
                <h4 class="chart-title">热门食物 TOP 5</h4>
                <div class="foods-list">
                  <div
                    v-for="food in report.mealAnalysis.popularFoods.slice(0, 5)"
                    :key="food.foodName"
                    class="food-item"
                  >
                    <span class="food-name">{{ food.foodName }}</span>
                    <span class="food-frequency">{{ food.frequency }}次</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 健康建议卡片 -->
          <div class="report-card recommendations-card">
            <div class="card-header">
              <h3 class="card-title">
                <span class="card-icon">💡</span>
                健康建议
              </h3>
            </div>

            <div class="card-content">
              <!-- 优势 -->
              <div v-if="report.healthAssessment.strengths.length > 0" class="strengths-section">
                <h4 class="section-title">
                  <span class="section-icon">✅</span>
                  做得很好
                </h4>
                <ul class="strengths-list">
                  <li v-for="strength in report.healthAssessment.strengths" :key="strength">
                    {{ strength }}
                  </li>
                </ul>
              </div>

              <!-- 改进建议 -->
              <div v-if="report.healthAssessment.recommendations.length > 0" class="recommendations-section">
                <h4 class="section-title">
                  <span class="section-icon">🎯</span>
                  改进建议
                </h4>
                <ul class="recommendations-list">
                  <li v-for="recommendation in report.healthAssessment.recommendations" :key="recommendation">
                    {{ recommendation }}
                  </li>
                </ul>
              </div>

              <!-- 需要关注的领域 -->
              <div v-if="report.healthAssessment.improvementAreas.length > 0" class="improvement-section">
                <h4 class="section-title">
                  <span class="section-icon">⚠️</span>
                  需要关注
                </h4>
                <ul class="improvement-list">
                  <li v-for="area in report.healthAssessment.improvementAreas" :key="area">
                    {{ area }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 报告生成时间 -->
        <div class="report-footer">
          <p class="generation-time">
            报告生成时间：{{ formatDateTime(report.generatedAt) }}
          </p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import TopNavbar from '../components/TopNavbar.vue'
import {
  generateHealthReport,
  type HealthReport,
  type ReportPeriod
} from '../utils/reportsApi'

// 响应式数据
const selectedPeriod = ref<ReportPeriod>('week')
const report = ref<HealthReport | null>(null)
const isLoading = ref(false)
const error = ref('')

// 时间范围选项
const periods = [
  { value: 'week' as ReportPeriod, label: '最近一周' },
  { value: 'month' as ReportPeriod, label: '最近一月' },
  { value: 'quarter' as ReportPeriod, label: '最近三月' },
  { value: 'year' as ReportPeriod, label: '最近一年' }
]

// 加载报告数据
const loadReport = async () => {
  try {
    isLoading.value = true
    error.value = ''

    const reportData = await generateHealthReport(selectedPeriod.value)
    report.value = reportData
  } catch (err) {
    console.error('加载健康报告失败:', err)
    error.value = err instanceof Error ? err.message : '加载健康报告失败'
  } finally {
    isLoading.value = false
  }
}

// 获取评分等级样式类
const getScoreClass = (score: number): string => {
  if (score >= 80) return 'excellent'
  if (score >= 60) return 'good'
  if (score >= 40) return 'fair'
  return 'poor'
}

// 获取趋势图标
const getTrendIcon = (trend: string): string => {
  switch (trend) {
    case 'up':
    case 'improving':
      return '📈'
    case 'down':
    case 'declining':
      return '📉'
    case 'stable':
    default:
      return '➡️'
  }
}

// 获取趋势标签
const getTrendLabel = (key: string): string => {
  switch (key) {
    case 'nutrition':
      return '营养'
    case 'goals':
      return '目标'
    case 'consistency':
      return '规律性'
    default:
      return key
  }
}

// 获取时间范围文本
const getPeriodText = (period: ReportPeriod): string => {
  const periodMap = {
    week: '最近一周',
    month: '最近一月',
    quarter: '最近三月',
    year: '最近一年'
  }
  return periodMap[period] || period
}

// 获取膳食类型名称
const getMealTypeName = (mealType: string): string => {
  const mealTypeMap: Record<string, string> = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '加餐'
  }
  return mealTypeMap[mealType] || mealType
}

// 格式化日期时间
const formatDateTime = (dateTimeString: string): string => {
  const date = new Date(dateTimeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听时间范围变化
watch(selectedPeriod, () => {
  loadReport()
})

// 组件挂载时加载数据
onMounted(() => {
  loadReport()
})
</script>

<style scoped>
.reports-container {
  min-height: 100vh;
  background: var(--background-secondary);
}

/* 主要内容区域 */
.main-content {
  padding: 120px var(--spacing-lg) var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.page-title {
  font-size: var(--font-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.title-icon {
  font-size: var(--font-3xl);
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-lg);
  margin: 0;
}

/* 时间范围选择器 */
.period-selector-section {
  margin-bottom: var(--spacing-2xl);
}

.period-selector-card {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-card);
  text-align: center;
}

.section-title {
  font-size: var(--font-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.period-buttons {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  flex-wrap: wrap;
}

.period-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-large);
  background: var(--background-primary);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.period-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.period-btn.active {
  background: var(--gradient-primary);
  border-color: var(--primary-color);
  color: var(--text-white);
  font-weight: var(--font-semibold);
}

/* 加载和错误状态 */
.loading-section, .error-section {
  margin-bottom: var(--spacing-2xl);
}

.loading-card, .error-card {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-card);
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-lg);
  margin: 0;
}

.error-icon {
  font-size: var(--font-4xl);
  margin-bottom: var(--spacing-lg);
}

.error-title {
  color: var(--text-primary);
  font-size: var(--font-2xl);
  font-weight: var(--font-semibold);
  margin: 0 0 var(--spacing-sm) 0;
}

.error-message {
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xl) 0;
}

.retry-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--gradient-primary);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-medium);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: var(--transition-normal);
}

.retry-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* 报告内容 */
.report-content {
  animation: slideUp var(--transition-normal);
}

/* 综合评估概览 */
.assessment-overview {
  margin-bottom: var(--spacing-2xl);
}

.overview-card {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-card);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.card-title {
  font-size: var(--font-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-icon {
  font-size: var(--font-xl);
}

.report-period {
  background: var(--background-light);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  font-weight: var(--font-medium);
}

/* 评分显示 */
.score-display {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.overall-score {
  display: flex;
  justify-content: center;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-full);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background: conic-gradient(from 0deg, var(--border-light), var(--border-light));
}

.score-circle.excellent {
  background: conic-gradient(from 0deg, var(--success-color) 0%, var(--success-color) 80%, var(--border-light) 80%);
}

.score-circle.good {
  background: conic-gradient(from 0deg, var(--primary-color) 0%, var(--primary-color) 60%, var(--border-light) 60%);
}

.score-circle.fair {
  background: conic-gradient(from 0deg, var(--warning-color) 0%, var(--warning-color) 40%, var(--border-light) 40%);
}

.score-circle.poor {
  background: conic-gradient(from 0deg, var(--error-color) 0%, var(--error-color) 20%, var(--border-light) 20%);
}

.score-circle::before {
  content: '';
  position: absolute;
  width: 90px;
  height: 90px;
  background: var(--background-primary);
  border-radius: var(--radius-full);
}

.score-number {
  font-size: var(--font-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  z-index: 1;
}

.score-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  z-index: 1;
}

.score-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.score-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.score-bar {
  flex: 1;
  height: 8px;
  background: var(--border-light);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-small);
  transition: width 0.8s ease;
}

.score-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 120px;
}

.score-name {
  color: var(--text-secondary);
  font-size: var(--font-sm);
}

.score-value {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* 趋势指示器 */
.trend-indicators {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  justify-content: center;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--background-light);
  border-radius: var(--radius-full);
}

.trend-icon {
  font-size: var(--font-xl);
}

.trend-icon.improving {
  color: var(--success-color);
}

.trend-icon.declining {
  color: var(--error-color);
}

.trend-icon.stable {
  color: var(--text-secondary);
}

.trend-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* 报告网格 */
.report-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.report-card {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-card);
  transition: var(--transition-normal);
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.report-card .card-header {
  margin-bottom: var(--spacing-xl);
}

.report-card .card-title {
  font-size: var(--font-xl);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 营养卡片样式 */
.nutrition-stats, .goals-stats, .meals-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--background-light);
  border-radius: var(--radius-large);
}

.stat-value {
  font-size: var(--font-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 宏量营养素图表 */
.macronutrient-chart, .meal-distribution {
  background: var(--background-light);
  padding: var(--spacing-xl);
  border-radius: var(--radius-large);
}

.chart-title {
  font-size: var(--font-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.macro-bars, .distribution-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.macro-item, .distribution-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.macro-bar, .distribution-bar {
  flex: 1;
  height: 8px;
  background: var(--border-light);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.macro-fill, .distribution-fill {
  height: 100%;
  border-radius: var(--radius-small);
  transition: width 0.8s ease;
}

.macro-fill.protein, .distribution-fill.breakfast {
  background: var(--gradient-success);
}

.macro-fill.fat, .distribution-fill.lunch {
  background: linear-gradient(90deg, var(--warning-color), var(--warning-light));
}

.macro-fill.carb, .distribution-fill.dinner {
  background: var(--gradient-secondary);
}

.distribution-fill.snack {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.macro-info, .distribution-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 120px;
}

.macro-name, .distribution-name {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.macro-value, .distribution-value {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* 目标列表 */
.goals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.goal-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.goal-info {
  flex: 1;
}

.goal-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.goal-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.8s ease;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  min-width: 40px;
}

.goal-trend {
  font-size: 1.25rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: #f1f5f9;
}

.goal-trend.up {
  color: #10b981;
}

.goal-trend.down {
  color: #ef4444;
}

.goal-trend.stable {
  color: #64748b;
}

/* 热门食物 */
.popular-foods {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 12px;
}

.foods-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.food-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.food-name {
  font-weight: 500;
  color: #1e293b;
}

.food-frequency {
  font-size: 0.875rem;
  color: #64748b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

/* 建议卡片 */
.recommendations-card .card-content {
  gap: 2rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-icon {
  font-size: 1.125rem;
}

.strengths-list, .recommendations-list, .improvement-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.strengths-list li {
  padding: 0.75rem;
  background: #f0fdf4;
  border-left: 4px solid #10b981;
  border-radius: 8px;
  color: #065f46;
}

.recommendations-list li {
  padding: 0.75rem;
  background: #eff6ff;
  border-left: 4px solid #3b82f6;
  border-radius: 8px;
  color: #1e40af;
}

.improvement-list li {
  padding: 0.75rem;
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
}

/* 报告底部 */
.report-footer {
  text-align: center;
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-2xl);
}

.generation-time {
  color: var(--text-secondary);
  font-size: var(--font-sm);
  margin: 0;
}

@media (max-width: 768px) {
  .main-content {
    padding: 120px var(--spacing-md) var(--spacing-xl);
  }

  .period-buttons {
    justify-content: center;
  }

  .score-display {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .trend-indicators {
    flex-wrap: wrap;
    justify-content: center;
  }

  .report-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .nutrition-stats, .goals-stats, .meals-stats {
    grid-template-columns: 1fr;
  }

  .overview-card, .report-card {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: var(--font-3xl);
  }

  .period-buttons {
    flex-wrap: wrap;
  }

  .period-btn {
    flex: 1;
    min-width: 120px;
  }
}
</style>

<style scoped>
.page-container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0;
}

.page-content {
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-card {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.placeholder-card h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.placeholder-card p {
  color: #64748b;
  margin: 0 0 1rem 0;
}

.placeholder-card ul {
  text-align: left;
  color: #64748b;
  margin: 0 0 2rem 0;
}

.placeholder-card li {
  margin: 0.5rem 0;
}

.status-badge {
  display: inline-block;
  background: #fef3c7;
  color: #d97706;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}
</style>
