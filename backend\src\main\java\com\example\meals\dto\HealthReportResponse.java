package com.example.meals.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 健康报告响应DTO
 * {{ AURA-X: Add - 创建健康报告响应数据结构. Approval: 寸止. }}
 */
public class HealthReportResponse {
    
    private String period;
    private String startDate;
    private String endDate;
    private LocalDateTime generatedAt;
    
    // 营养摄入报告
    private NutritionReportData nutrition;
    
    // 健康目标进度报告
    private GoalProgressReportData goalProgress;
    
    // 膳食记录分析
    private MealAnalysisReportData mealAnalysis;
    
    // 综合健康评估
    private HealthAssessmentReportData healthAssessment;
    
    // 构造函数
    public HealthReportResponse() {}
    
    // Getter 和 Setter 方法
    public String getPeriod() { return period; }
    public void setPeriod(String period) { this.period = period; }
    
    public String getStartDate() { return startDate; }
    public void setStartDate(String startDate) { this.startDate = startDate; }
    
    public String getEndDate() { return endDate; }
    public void setEndDate(String endDate) { this.endDate = endDate; }
    
    public LocalDateTime getGeneratedAt() { return generatedAt; }
    public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
    
    public NutritionReportData getNutrition() { return nutrition; }
    public void setNutrition(NutritionReportData nutrition) { this.nutrition = nutrition; }
    
    public GoalProgressReportData getGoalProgress() { return goalProgress; }
    public void setGoalProgress(GoalProgressReportData goalProgress) { this.goalProgress = goalProgress; }
    
    public MealAnalysisReportData getMealAnalysis() { return mealAnalysis; }
    public void setMealAnalysis(MealAnalysisReportData mealAnalysis) { this.mealAnalysis = mealAnalysis; }
    
    public HealthAssessmentReportData getHealthAssessment() { return healthAssessment; }
    public void setHealthAssessment(HealthAssessmentReportData healthAssessment) { this.healthAssessment = healthAssessment; }
    
    // 内部数据类
    public static class NutritionReportData {
        private Double totalCalories;
        private Double averageCalories;
        private Map<String, Double> macronutrients;
        private Map<String, Double> macronutrientPercentages;
        private NutritionRecommendationResponse recommendations;
        private Integer complianceScore;
        private List<DailyNutrientData> dailyNutrients;
        
        // Getter 和 Setter 方法
        public Double getTotalCalories() { return totalCalories; }
        public void setTotalCalories(Double totalCalories) { this.totalCalories = totalCalories; }
        
        public Double getAverageCalories() { return averageCalories; }
        public void setAverageCalories(Double averageCalories) { this.averageCalories = averageCalories; }
        
        public Map<String, Double> getMacronutrients() { return macronutrients; }
        public void setMacronutrients(Map<String, Double> macronutrients) { this.macronutrients = macronutrients; }
        
        public Map<String, Double> getMacronutrientPercentages() { return macronutrientPercentages; }
        public void setMacronutrientPercentages(Map<String, Double> macronutrientPercentages) { this.macronutrientPercentages = macronutrientPercentages; }
        
        public NutritionRecommendationResponse getRecommendations() { return recommendations; }
        public void setRecommendations(NutritionRecommendationResponse recommendations) { this.recommendations = recommendations; }
        
        public Integer getComplianceScore() { return complianceScore; }
        public void setComplianceScore(Integer complianceScore) { this.complianceScore = complianceScore; }
        
        public List<DailyNutrientData> getDailyNutrients() { return dailyNutrients; }
        public void setDailyNutrients(List<DailyNutrientData> dailyNutrients) { this.dailyNutrients = dailyNutrients; }
    }
    
    public static class GoalProgressReportData {
        private Integer activeGoals;
        private Integer completedGoals;
        private Integer totalGoals;
        private Double overallProgress;
        private Integer currentStreak;
        private List<GoalDetailData> goalDetails;
        
        // Getter 和 Setter 方法
        public Integer getActiveGoals() { return activeGoals; }
        public void setActiveGoals(Integer activeGoals) { this.activeGoals = activeGoals; }
        
        public Integer getCompletedGoals() { return completedGoals; }
        public void setCompletedGoals(Integer completedGoals) { this.completedGoals = completedGoals; }
        
        public Integer getTotalGoals() { return totalGoals; }
        public void setTotalGoals(Integer totalGoals) { this.totalGoals = totalGoals; }
        
        public Double getOverallProgress() { return overallProgress; }
        public void setOverallProgress(Double overallProgress) { this.overallProgress = overallProgress; }
        
        public Integer getCurrentStreak() { return currentStreak; }
        public void setCurrentStreak(Integer currentStreak) { this.currentStreak = currentStreak; }
        
        public List<GoalDetailData> getGoalDetails() { return goalDetails; }
        public void setGoalDetails(List<GoalDetailData> goalDetails) { this.goalDetails = goalDetails; }
    }
    
    public static class MealAnalysisReportData {
        private Integer totalMeals;
        private Double averageMealsPerDay;
        private Map<String, Integer> mealTypeDistribution;
        private Map<String, Integer> eatingPatterns;
        private List<PopularFoodData> popularFoods;
        
        // Getter 和 Setter 方法
        public Integer getTotalMeals() { return totalMeals; }
        public void setTotalMeals(Integer totalMeals) { this.totalMeals = totalMeals; }
        
        public Double getAverageMealsPerDay() { return averageMealsPerDay; }
        public void setAverageMealsPerDay(Double averageMealsPerDay) { this.averageMealsPerDay = averageMealsPerDay; }
        
        public Map<String, Integer> getMealTypeDistribution() { return mealTypeDistribution; }
        public void setMealTypeDistribution(Map<String, Integer> mealTypeDistribution) { this.mealTypeDistribution = mealTypeDistribution; }
        
        public Map<String, Integer> getEatingPatterns() { return eatingPatterns; }
        public void setEatingPatterns(Map<String, Integer> eatingPatterns) { this.eatingPatterns = eatingPatterns; }
        
        public List<PopularFoodData> getPopularFoods() { return popularFoods; }
        public void setPopularFoods(List<PopularFoodData> popularFoods) { this.popularFoods = popularFoods; }
    }
    
    public static class HealthAssessmentReportData {
        private Integer overallScore;
        private Integer nutritionScore;
        private Integer goalAchievementScore;
        private Integer consistencyScore;
        private List<String> improvementAreas;
        private List<String> strengths;
        private List<String> recommendations;
        private Map<String, String> trends;
        private ComparisonData comparisonData;
        
        // Getter 和 Setter 方法
        public Integer getOverallScore() { return overallScore; }
        public void setOverallScore(Integer overallScore) { this.overallScore = overallScore; }
        
        public Integer getNutritionScore() { return nutritionScore; }
        public void setNutritionScore(Integer nutritionScore) { this.nutritionScore = nutritionScore; }
        
        public Integer getGoalAchievementScore() { return goalAchievementScore; }
        public void setGoalAchievementScore(Integer goalAchievementScore) { this.goalAchievementScore = goalAchievementScore; }
        
        public Integer getConsistencyScore() { return consistencyScore; }
        public void setConsistencyScore(Integer consistencyScore) { this.consistencyScore = consistencyScore; }
        
        public List<String> getImprovementAreas() { return improvementAreas; }
        public void setImprovementAreas(List<String> improvementAreas) { this.improvementAreas = improvementAreas; }
        
        public List<String> getStrengths() { return strengths; }
        public void setStrengths(List<String> strengths) { this.strengths = strengths; }
        
        public List<String> getRecommendations() { return recommendations; }
        public void setRecommendations(List<String> recommendations) { this.recommendations = recommendations; }
        
        public Map<String, String> getTrends() { return trends; }
        public void setTrends(Map<String, String> trends) { this.trends = trends; }
        
        public ComparisonData getComparisonData() { return comparisonData; }
        public void setComparisonData(ComparisonData comparisonData) { this.comparisonData = comparisonData; }
    }
    
    // 辅助数据类
    public static class DailyNutrientData {
        private String date;
        private Double calories;
        private Double protein;
        private Double fat;
        private Double carbohydrate;
        private Double fiber;
        
        // 构造函数和 Getter/Setter 方法
        public DailyNutrientData() {}
        
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public Double getCalories() { return calories; }
        public void setCalories(Double calories) { this.calories = calories; }
        
        public Double getProtein() { return protein; }
        public void setProtein(Double protein) { this.protein = protein; }
        
        public Double getFat() { return fat; }
        public void setFat(Double fat) { this.fat = fat; }
        
        public Double getCarbohydrate() { return carbohydrate; }
        public void setCarbohydrate(Double carbohydrate) { this.carbohydrate = carbohydrate; }
        
        public Double getFiber() { return fiber; }
        public void setFiber(Double fiber) { this.fiber = fiber; }
    }
    
    public static class GoalDetailData {
        private Long goalId;
        private String goalName;
        private String goalType;
        private Double targetValue;
        private Double currentValue;
        private Double progress;
        private Boolean isCompleted;
        private String trend;
        
        // 构造函数和 Getter/Setter 方法
        public GoalDetailData() {}
        
        public Long getGoalId() { return goalId; }
        public void setGoalId(Long goalId) { this.goalId = goalId; }
        
        public String getGoalName() { return goalName; }
        public void setGoalName(String goalName) { this.goalName = goalName; }
        
        public String getGoalType() { return goalType; }
        public void setGoalType(String goalType) { this.goalType = goalType; }
        
        public Double getTargetValue() { return targetValue; }
        public void setTargetValue(Double targetValue) { this.targetValue = targetValue; }
        
        public Double getCurrentValue() { return currentValue; }
        public void setCurrentValue(Double currentValue) { this.currentValue = currentValue; }
        
        public Double getProgress() { return progress; }
        public void setProgress(Double progress) { this.progress = progress; }
        
        public Boolean getIsCompleted() { return isCompleted; }
        public void setIsCompleted(Boolean isCompleted) { this.isCompleted = isCompleted; }
        
        public String getTrend() { return trend; }
        public void setTrend(String trend) { this.trend = trend; }
    }
    
    public static class PopularFoodData {
        private String foodName;
        private Integer frequency;
        private Double totalCalories;
        
        // 构造函数和 Getter/Setter 方法
        public PopularFoodData() {}
        
        public String getFoodName() { return foodName; }
        public void setFoodName(String foodName) { this.foodName = foodName; }
        
        public Integer getFrequency() { return frequency; }
        public void setFrequency(Integer frequency) { this.frequency = frequency; }
        
        public Double getTotalCalories() { return totalCalories; }
        public void setTotalCalories(Double totalCalories) { this.totalCalories = totalCalories; }
    }
    
    public static class ComparisonData {
        private Map<String, Integer> previousPeriod;
        private Integer improvement;
        
        // 构造函数和 Getter/Setter 方法
        public ComparisonData() {}
        
        public Map<String, Integer> getPreviousPeriod() { return previousPeriod; }
        public void setPreviousPeriod(Map<String, Integer> previousPeriod) { this.previousPeriod = previousPeriod; }
        
        public Integer getImprovement() { return improvement; }
        public void setImprovement(Integer improvement) { this.improvement = improvement; }
    }
}
