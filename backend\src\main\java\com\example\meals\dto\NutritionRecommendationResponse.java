package com.example.meals.dto;

/**
 * 营养推荐摄入量响应DTO
 * {{ AURA-X: Add - 创建营养推荐摄入量响应数据结构. Approval: 寸止. }}
 */
public class NutritionRecommendationResponse {
    
    private Double calories;
    private Double protein;
    private Double fat;
    private Double carbohydrate;
    private Double fiber;
    private String calculationMethod;
    private UserBasicInfo userInfo;
    
    // 构造函数
    public NutritionRecommendationResponse() {}
    
    public NutritionRecommendationResponse(Double calories, Double protein, Double fat, Double carbohydrate, Double fiber) {
        this.calories = calories;
        this.protein = protein;
        this.fat = fat;
        this.carbohydrate = carbohydrate;
        this.fiber = fiber;
        this.calculationMethod = "Harris-Benedict Formula";
    }
    
    // Getter 和 Setter 方法
    public Double getCalories() { return calories; }
    public void setCalories(Double calories) { this.calories = calories; }
    
    public Double getProtein() { return protein; }
    public void setProtein(Double protein) { this.protein = protein; }
    
    public Double getFat() { return fat; }
    public void setFat(Double fat) { this.fat = fat; }
    
    public Double getCarbohydrate() { return carbohydrate; }
    public void setCarbohydrate(Double carbohydrate) { this.carbohydrate = carbohydrate; }
    
    public Double getFiber() { return fiber; }
    public void setFiber(Double fiber) { this.fiber = fiber; }
    
    public String getCalculationMethod() { return calculationMethod; }
    public void setCalculationMethod(String calculationMethod) { this.calculationMethod = calculationMethod; }
    
    public UserBasicInfo getUserInfo() { return userInfo; }
    public void setUserInfo(UserBasicInfo userInfo) { this.userInfo = userInfo; }
    
    // 用户基本信息内部类
    public static class UserBasicInfo {
        private Integer age;
        private String gender;
        private Double height;
        private Double weight;
        private Double bmr;
        private String activityLevel;
        
        // 构造函数
        public UserBasicInfo() {}
        
        public UserBasicInfo(Integer age, String gender, Double height, Double weight, Double bmr, String activityLevel) {
            this.age = age;
            this.gender = gender;
            this.height = height;
            this.weight = weight;
            this.bmr = bmr;
            this.activityLevel = activityLevel;
        }
        
        // Getter 和 Setter 方法
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        
        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }
        
        public Double getHeight() { return height; }
        public void setHeight(Double height) { this.height = height; }
        
        public Double getWeight() { return weight; }
        public void setWeight(Double weight) { this.weight = weight; }
        
        public Double getBmr() { return bmr; }
        public void setBmr(Double bmr) { this.bmr = bmr; }
        
        public String getActivityLevel() { return activityLevel; }
        public void setActivityLevel(String activityLevel) { this.activityLevel = activityLevel; }
    }
}
