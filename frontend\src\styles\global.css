/* 全局样式文件 - 膳食营养分析平台 */

/* ===== CSS变量定义 ===== */
:root {
  /* 主色调 - 健康绿色系 */
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --primary-green: #16a085;
  --primary-green-light: #48c9b0;
  --primary-green-dark: #138d75;
  
  /* 辅助色彩 */
  --secondary-color: #3498db;
  --secondary-light: #5dade2;
  --secondary-dark: #2980b9;
  
  /* 功能色彩 */
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  --success-light: #58d68d;
  --warning-color: #f39c12;
  --warning-light: #f8c471;
  --error-color: #e74c3c;
  --error-light: #ec7063;
  --info-color: #3498db;
  
  /* 文本颜色 */
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --text-muted: #95a5a6;
  --text-white: #ffffff;
  
  /* 背景颜色 */
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-light: #ecf0f1;
  --background-dark: #34495e;
  --background-overlay: rgba(0, 0, 0, 0.5);
  
  /* 边框和分割线 */
  --border-color: #e0e6ed;
  --border-light: #ecf0f1;
  --border-dark: #bdc3c7;
  
  /* 阴影效果 */
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-heavy: rgba(0, 0, 0, 0.25);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  /* 渐变效果 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
  --gradient-success: linear-gradient(135deg, var(--success-color), var(--success-light));
  --gradient-overlay: linear-gradient(135deg, rgba(22, 160, 133, 0.9), rgba(26, 188, 156, 0.9));
  
  /* 圆角半径 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* 字体大小 */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-base: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-2xl: 1.5rem;
  --font-3xl: 1.875rem;
  --font-4xl: 2.25rem;
  
  /* 字体权重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== 通用页面布局 ===== */
.page-container {
  min-height: 100vh;
  background: var(--background-secondary);
  padding: var(--spacing-xl);
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: var(--font-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* ===== 通用卡片样式 ===== */
.card {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  font-size: var(--font-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.card-subtitle {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.card-body {
  margin-bottom: var(--spacing-lg);
}

.card-footer {
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

/* ===== 通用按钮样式 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-base);
  font-weight: var(--font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: var(--text-white);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-success {
  background: var(--gradient-success);
  color: var(--text-white);
}

.btn-success:hover:not(:disabled) {
  background: var(--success-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-white);
}

.btn-ghost {
  background: transparent;
  color: var(--primary-color);
}

.btn-ghost:hover:not(:disabled) {
  background: rgba(22, 160, 133, 0.1);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-lg);
}

.btn-full {
  width: 100%;
}

/* ===== 通用表单样式 ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: var(--font-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: var(--font-base);
  font-family: inherit;
  transition: var(--transition-normal);
  background: var(--background-primary);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

.form-control:invalid {
  border-color: var(--error-color);
}

.form-help {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.form-error {
  font-size: var(--font-xs);
  color: var(--error-color);
  margin-top: var(--spacing-xs);
}

/* ===== 通用工具类 ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.m-0 { margin: 0; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }
.ml-auto { margin-left: auto; }
.mr-auto { margin-right: auto; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-medium); }
.rounded-lg { border-radius: var(--radius-large); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-card); }
.shadow-lg { box-shadow: var(--shadow-hover); }

/* ===== 动画效果 ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in {
  animation: fadeIn var(--transition-normal);
}

.slide-up {
  animation: slideUp var(--transition-normal);
}

.pulse {
  animation: pulse 2s infinite;
}

/* ===== 响应式断点 ===== */
@media (max-width: 1200px) {
  .page-container {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }
  
  .page-title {
    font-size: var(--font-2xl);
  }
  
  .card {
    padding: var(--spacing-md);
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
  }
}

@media (max-width: 480px) {
  .page-container {
    padding: var(--spacing-sm);
  }
  
  .page-title {
    font-size: var(--font-xl);
  }
  
  .card {
    padding: var(--spacing-sm);
  }
  
  .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
  }
}
