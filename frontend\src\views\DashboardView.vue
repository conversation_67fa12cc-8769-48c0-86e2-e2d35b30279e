<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <TopNavbar
      :show-avatar="true"
      :show-logout-button="true"
      @avatar-click="triggerAvatarUpload"
      @logout="handleLogout"
    />

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">欢迎回来，{{ user?.username }}！</h1>
          <p class="welcome-subtitle">开始您的健康饮食之旅，让我们一起追踪您的营养目标</p>
          <div class="quick-actions">
            <router-link to="/nutrition" class="quick-action-btn primary">
              <span class="action-icon">🥗</span>
              <span>营养分析</span>
            </router-link>
            <router-link to="/meals" class="quick-action-btn secondary">
              <span class="action-icon">🍽️</span>
              <span>记录膳食</span>
            </router-link>
          </div>
        </div>
      </section>

      <!-- 统计卡片区域 -->
      <section class="stats-section">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-info">
              <h3>今日营养摄入</h3>
              <p class="stat-number">{{ todayStats.calories || 0 }}</p>
              <p class="stat-unit">千卡</p>
              <div class="stat-progress">
                <div class="progress-bar" :style="{ width: `${Math.min(100, (todayStats.calories || 0) / 2000 * 100)}%` }"></div>
              </div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🍽️</div>
            <div class="stat-info">
              <h3>今日膳食记录</h3>
              <p class="stat-number">{{ todayStats.meals || 0 }}</p>
              <p class="stat-unit">餐</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-info">
              <h3>健康目标</h3>
              <p class="stat-number">{{ todayStats.goalProgress || 0 }}%</p>
              <p class="stat-unit">完成度</p>
              <div class="stat-progress">
                <div class="progress-bar" :style="{ width: `${todayStats.goalProgress || 0}%` }"></div>
              </div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">📈</div>
            <div class="stat-info">
              <h3>连续记录</h3>
              <p class="stat-number">{{ todayStats.streak || 0 }}</p>
              <p class="stat-unit">天</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 功能模块区域 -->
      <section class="features-section">
        <h2 class="section-title">功能中心</h2>
        <div class="features-grid">
          <router-link
            v-for="feature in features"
            :key="feature.id"
            :to="feature.path"
            class="feature-card"
          >
            <div class="feature-icon" v-html="feature.icon"></div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
            <div class="feature-status" :class="feature.status">
              {{ feature.statusText }}
            </div>
          </router-link>
        </div>
      </section>

      <!-- 最近活动区域 -->
      <section class="activity-section">
        <h2 class="section-title">最近活动</h2>
        <div class="activity-list">
          <div v-if="recentActivities.length === 0" class="no-activity">
            <div class="no-activity-icon">📝</div>
            <p>暂无活动记录</p>
            <p class="no-activity-hint">开始记录您的膳食和营养分析吧！</p>
          </div>
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">{{ activity.icon }}</div>
            <div class="activity-content">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
              <span class="activity-time">{{ activity.time }}</span>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 隐藏的头像上传文件输入 -->
    <input
      ref="avatarFileInput"
      type="file"
      accept="image/jpeg,image/jpg,image/png,image/gif"
      style="display: none"
      @change="handleAvatarFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import TopNavbar from '../components/TopNavbar.vue'
import { uploadUserAvatar } from '../utils/userApi'
import { message } from '../utils/message'
import { UserManager } from '../utils/auth'
import { validateFileType, validateFileSize, getMaxFileSizeText, getAllowedTypesText } from '../config/api'

const router = useRouter()
const { user, isLoggedIn, logout: authLogout, updateUserState } = useAuth()
const avatarFileInput = ref<HTMLInputElement>()

// 头像上传状态
const isUploading = ref(false)

// 类型定义
interface Activity {
  id: number
  title: string
  description: string
  icon: string
  time: string
}

// 今日统计数据
const todayStats = reactive({
  calories: 0,
  meals: 0,
  goalProgress: 0,
  streak: 0
})

// 功能模块数据
const features = ref([
  {
    id: 1,
    title: '营养分析',
    description: '分析食物营养成分，制定健康饮食计划',
    icon: '🥗',
    path: '/nutrition',
    status: 'developing',
    statusText: '开发中'
  },
  {
    id: 2,
    title: '膳食记录',
    description: '记录每日饮食，追踪营养摄入情况',
    icon: '🍽️',
    path: '/meals',
    status: 'developing',
    statusText: '开发中'
  },
  {
    id: 3,
    title: '健康目标',
    description: '设定和管理个人健康目标',
    icon: '🎯',
    path: '/goals',
    status: 'developing',
    statusText: '开发中'
  },
  {
    id: 4,
    title: '健康报告',
    description: '查看详细的健康分析报告',
    icon: '📊',
    path: '/reports',
    status: 'available',
    statusText: '可用'
  },
  {
    id: 5,
    title: '个人资料',
    description: '管理个人信息和偏好设置',
    icon: '👤',
    path: '/profile',
    status: 'developing',
    statusText: '开发中'
  },
  {
    id: 6,
    title: '系统设置',
    description: '调整应用设置和通知偏好',
    icon: '⚙️',
    path: '/settings',
    status: 'developing',
    statusText: '开发中'
  }
])

// 最近活动数据
const recentActivities = ref<Activity[]>([])

// 触发头像上传
const triggerAvatarUpload = () => {
  if (isUploading.value) return
  avatarFileInput.value?.click()
}

// 处理头像文件选择
const handleAvatarFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) {
    isUploading.value = false
    return
  }

  // 文件验证
  if (!validateFileType(file)) {
    message.error(`只支持 ${getAllowedTypesText()} 格式的图片`)
    return
  }

  if (!validateFileSize(file)) {
    message.error(`文件大小不能超过 ${getMaxFileSizeText()}`)
    return
  }

  isUploading.value = true

  try {
    const response = await uploadUserAvatar(file)
    if (response.success) {
      // 更新用户头像
      if (user.value) {
        user.value.avatar = response.data
        UserManager.setUser(user.value)
      }

      message.success('头像上传成功')
      updateUserState()
    } else {
      message.error(response.message || '头像上传失败')
    }
  } catch (error) {
    message.error('头像上传失败，请稍后重试')
  } finally {
    target.value = ''
    isUploading.value = false
  }
}

// 加载统计数据
const loadDashboardData = async () => {
  try {
    // TODO: 实际项目中应该从API获取数据
    // 示例：
    // const statsResponse = await getUserStats()
    // if (statsResponse.success) {
    //   Object.assign(todayStats, statsResponse.data)
    // }

    // const activitiesResponse = await getRecentActivities()
    // if (activitiesResponse.success) {
    //   recentActivities.value = activitiesResponse.data
    // }
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

// 处理登出
const handleLogout = () => {
  authLogout()
  updateUserState()
  router.push('/')
}

onMounted(() => {
  // 检查登录状态
  if (!isLoggedIn.value) {
    router.push('/login')
    return
  }



  // 加载用户数据和统计信息
  loadDashboardData()
})
</script>

<style scoped>
/* DashboardView 特定样式 - 使用全局CSS变量 */

/* 全局样式 */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 - 与首页相同 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 导航栏现在使用共享组件 TopNavbar */

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  line-height: 1.2;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.quick-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.quick-action-btn.primary {
  /* 使用直接颜色值确保显示 */
  background: #16a085;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white !important;
  border: 2px solid #16a085;
  /* 确保文字可见性 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quick-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #138d75;
  border-color: #138d75;
}

.quick-action-btn.secondary {
  background: white;
  color: #16a085 !important;
  border: 2px solid #16a085;
  /* 确保文字可见性 */
  font-weight: 600;
}

.quick-action-btn.secondary:hover {
  background: #16a085;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 1.25rem;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.stat-info h3 {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1;
}

.stat-unit {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0.25rem 0 0.75rem 0;
}

.stat-progress {
  width: 100%;
  height: 6px;
  background: var(--background-light);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 功能模块区域 */
.features-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
  color: inherit;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.feature-description {
  color: var(--text-secondary);
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.feature-status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
}

.feature-status.developing {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.feature-status.available {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

/* 最近活动区域 */
.activity-section {
  margin-bottom: 2rem;
}

.activity-list {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.no-activity {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.no-activity-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-activity p {
  margin: 0.5rem 0;
}

.no-activity-hint {
  font-size: 0.9rem;
  color: var(--text-light);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.activity-description {
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.8rem;
  color: var(--text-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .nav-content {
    padding: 0 1rem;
  }

  .nav-actions {
    gap: 0.5rem;
  }

  .welcome-content {
    padding: 2rem 1rem;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .quick-action-btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .welcome-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .brand-name {
    display: none;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .feature-card {
    padding: 1rem;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.modal-body {
  padding: 24px;
}

/* 浏览器兼容性支持 */
@supports not (-webkit-background-clip: text) {
  .welcome-title {
    color: var(--primary-color) !important;
    background: none;
  }
}
</style>
