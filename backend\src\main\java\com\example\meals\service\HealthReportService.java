package com.example.meals.service;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthReportResponse;
import com.example.meals.dto.NutritionRecommendationResponse;

/**
 * 健康报告服务接口
 * {{ AURA-X: Add - 创建健康报告服务接口. Approval: 寸止. }}
 */
public interface HealthReportService {
    
    /**
     * 生成用户健康报告
     * @param userId 用户ID
     * @param period 报告时间范围 (week/month/quarter/year)
     * @return 健康报告数据
     */
    Result<HealthReportResponse> generateHealthReport(Long userId, String period);
    
    /**
     * 计算用户营养推荐摄入量
     * @param userId 用户ID
     * @return 营养推荐数据
     */
    Result<NutritionRecommendationResponse> calculateNutritionRecommendations(Long userId);
    
    /**
     * 获取历史报告对比数据
     * @param userId 用户ID
     * @param period 当前报告时间范围
     * @return 历史对比数据
     */
    Result<Object> getHistoricalComparison(Long userId, String period);
}
