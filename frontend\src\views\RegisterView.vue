<template>
  <div class="register-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
      <div class="wave-pattern"></div>
    </div>

    <!-- 主要内容 -->
    <div class="register-content">
      <!-- 左侧注册表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>开启健康之旅</h2>
            <p>创建您的账户，开始科学的营养管理</p>
          </div>

          <!-- 成功提示 -->
          <div v-if="showSuccess" class="success-message">
            <div class="success-content">
              <svg class="success-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>{{ successMessage }}</span>
            </div>
          </div>

          <!-- 全局错误提示 -->
          <div v-if="globalError" class="global-error-message">
            <div class="error-content">
              <svg class="error-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ globalError }}</span>
            </div>
          </div>

          <form @submit.prevent="handleRegister" class="register-form">
            <div class="form-group">
              <label for="username" class="form-label">用户名</label>
              <div class="input-wrapper">
                <input
                  id="username"
                  v-model="registerForm.username"
                  type="text"
                  class="form-input"
                  :class="{ 'error': errors.username }"
                  placeholder="请输入用户名"
                  @blur="validateUsername"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </span>
              </div>
              <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
            </div>

            <div class="form-group">
              <label for="email" class="form-label">邮箱地址</label>
              <div class="input-wrapper">
                <input
                  id="email"
                  v-model="registerForm.email"
                  type="email"
                  class="form-input"
                  :class="{ 'error': errors.email }"
                  placeholder="请输入您的邮箱"
                  @blur="validateEmail"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </span>
              </div>
              <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
            </div>

            <!-- 邮箱验证码输入框 -->
            <div class="form-group">
              <label for="verificationCode" class="form-label">邮箱验证码</label>
              <div class="verification-input-wrapper">
                <input
                  id="verificationCode"
                  v-model="registerForm.verificationCode"
                  type="text"
                  class="form-input verification-input"
                  :class="{ 'error': errors.verificationCode }"
                  placeholder="请输入6位验证码"
                  maxlength="6"
                  @blur="validateVerificationCode"
                  @input="onVerificationCodeInput"
                />
                <button
                  type="button"
                  class="send-code-btn"
                  :class="{ 'disabled': !canSendCode || sendingCode }"
                  :disabled="!canSendCode || sendingCode"
                  @click="sendVerificationCodeHandler"
                >
                  {{ sendingCode ? '发送中...' : (countdown > 0 ? `${countdown}s后重发` : '发送验证码') }}
                </button>
              </div>
              <span v-if="errors.verificationCode" class="error-message">{{ errors.verificationCode }}</span>
            </div>

            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="input-wrapper">
                <input
                  id="password"
                  v-model="registerForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.password }"
                  placeholder="请设置密码（至少8位）"
                  @blur="validatePassword"
                  @input="checkPasswordStrength"
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.584 10.587A2 2 0 0 0 13.415 13.414" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
              <PasswordStrengthMeter v-if="registerForm.password" :password="registerForm.password" />
              <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
            </div>

            <div class="form-group">
              <label for="confirmPassword" class="form-label">确认密码</label>
              <div class="input-wrapper">
                <input
                  id="confirmPassword"
                  v-model="registerForm.confirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.confirmPassword }"
                  placeholder="请再次输入密码"
                  @blur="validateConfirmPassword"
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showConfirmPassword = !showConfirmPassword"
                >
                  <svg v-if="showConfirmPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.584 10.587A2 2 0 0 0 13.415 13.414" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
              <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
            </div>

            <div class="form-group">
              <label class="checkbox-wrapper">
                <input type="checkbox" v-model="agreeToTerms" />
                <span class="checkmark"></span>
                <span class="checkbox-label">
                  我已阅读并同意 
                  <a href="#" class="terms-link">用户协议</a> 和 
                  <a href="#" class="terms-link">隐私政策</a>
                </span>
              </label>
            </div>

            <button type="submit" class="register-button" :disabled="isLoading || !agreeToTerms">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span>{{ isLoading ? '注册中...' : '创建账户' }}</span>
            </button>
          </form>

          <div class="form-footer">
            <p>已有账户？ 
              <router-link to="/login" class="login-link">立即登录</router-link>
            </p>
          </div>
        </div>
      </div>

      <!-- 右侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="brand-logo">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                      fill="currentColor"/>
                <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h1 class="brand-title">膳食营养分析平台</h1>
          </div>
          
          <div class="benefits-list">
            <div class="benefit-item">
              <div class="benefit-icon">🎯</div>
              <div class="benefit-content">
                <h3>个性化目标</h3>
                <p>根据您的身体状况和健康目标，制定专属营养计划</p>
              </div>
            </div>
            
            <div class="benefit-item">
              <div class="benefit-icon">📊</div>
              <div class="benefit-content">
                <h3>智能分析</h3>
                <p>AI驱动的营养成分分析，让每一餐都更科学</p>
              </div>
            </div>
            
            <div class="benefit-item">
              <div class="benefit-icon">🏆</div>
              <div class="benefit-content">
                <h3>持续改进</h3>
                <p>跟踪您的进步，不断优化饮食建议</p>
              </div>
            </div>
          </div>
          
          <!-- <div class="stats">
            <div class="stat-item">
              <div class="stat-number">10,000+</div>
              <div class="stat-label">用户信赖</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50,000+</div>
              <div class="stat-label">食物数据库</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">98%</div>
              <div class="stat-label">准确率</div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { register, checkUsername, checkEmail, sendVerificationCode, type RegisterRequest, type ApiResponse, type User } from '../utils/userApi'
import PasswordStrengthMeter from '../components/PasswordStrengthMeter.vue'

// 类型定义
interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  verificationCode: string
}

interface FormErrors {
  username?: string
  email?: string
  password?: string
  confirmPassword?: string
  verificationCode?: string
}

interface PasswordStrength {
  score: number
  text: string
  class: string
  width: string
}

// 响应式数据
const router = useRouter()
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const agreeToTerms = ref(false)
const isLoading = ref(false)
const successMessage = ref('')
const showSuccess = ref(false)
const globalError = ref('')

const registerForm = reactive<RegisterForm>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

const errors = reactive<FormErrors>({})

// 验证码相关状态
const sendingCode = ref(false)
const countdown = ref(0)
const canSendCode = computed(() => {
  return registerForm.email && !errors.email && countdown.value === 0
})

// 密码强度计算
const passwordStrength = computed<PasswordStrength>(() => {
  const password = registerForm.password
  if (!password) return { score: 0, text: '', class: '', width: '0%' }
  
  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  
  // 复杂度检查
  if (/[a-z]/.test(password)) score += 1
  if (/[A-Z]/.test(password)) score += 1
  if (/[0-9]/.test(password)) score += 1
  if (/[^A-Za-z0-9]/.test(password)) score += 1
  
  if (score <= 2) {
    return { score, text: '弱', class: 'weak', width: '33%' }
  } else if (score <= 4) {
    return { score, text: '中等', class: 'medium', width: '66%' }
  } else {
    return { score, text: '强', class: 'strong', width: '100%' }
  }
})

// 验证方法
const validateUsername = () => {
  if (!registerForm.username) {
    errors.username = '请输入用户名'
  } else if (registerForm.username.length < 3) {
    errors.username = '用户名至少3个字符'
  } else if (registerForm.username.length > 20) {
    errors.username = '用户名不能超过20个字符'
  } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(registerForm.username)) {
    errors.username = '用户名只能包含字母、数字、下划线和中文'
  } else {
    errors.username = undefined
  }
}

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!registerForm.email) {
    errors.email = '请输入邮箱地址'
  } else if (!emailRegex.test(registerForm.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = undefined
  }
}



const validatePassword = () => {
  if (!registerForm.password) {
    errors.password = '请输入密码'
  } else if (registerForm.password.length < 8) {
    errors.password = '密码长度至少8位'
  } else if (passwordStrength.value.score < 2) {
    errors.password = '密码强度太弱，请包含字母、数字或特殊字符'
  } else {
    errors.password = undefined
  }
}

const validateConfirmPassword = () => {
  if (!registerForm.confirmPassword) {
    errors.confirmPassword = '请确认密码'
  } else if (registerForm.password !== registerForm.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = undefined
  }
}

const checkPasswordStrength = () => {
  // 实时检查密码强度，不显示错误信息
  if (registerForm.confirmPassword && registerForm.password !== registerForm.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = undefined
  }
}

// 验证码相关方法
const validateVerificationCode = () => {
  if (!registerForm.verificationCode) {
    errors.verificationCode = '请输入验证码'
  } else if (registerForm.verificationCode.length !== 6) {
    errors.verificationCode = '验证码必须是6位数字'
  } else if (!/^\d{6}$/.test(registerForm.verificationCode)) {
    errors.verificationCode = '验证码只能包含数字'
  } else {
    errors.verificationCode = undefined
  }
}

const onVerificationCodeInput = () => {
  // 限制只能输入数字
  registerForm.verificationCode = registerForm.verificationCode.replace(/\D/g, '')
  if (registerForm.verificationCode.length === 6) {
    validateVerificationCode()
  }
}

const sendVerificationCodeHandler = async () => {
  if (!canSendCode.value || sendingCode.value) return

  sendingCode.value = true
  try {
    const response = await sendVerificationCode(registerForm.email, 'REGISTER')
    if (response.success) {
      // 开始倒计时
      countdown.value = 60
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)

      // 显示成功提示
      globalError.value = ''
      // 可以添加成功提示
    } else {
      globalError.value = response.message || '发送验证码失败'
    }
  } catch (error) {
    globalError.value = '发送验证码失败，请检查网络连接'
  } finally {
    sendingCode.value = false
  }
}

// 注册处理
const handleRegister = async () => {
  // 清除之前的全局错误
  globalError.value = ''

  validateUsername()
  validateEmail()
  validatePassword()
  validateConfirmPassword()
  validateVerificationCode()

  if (errors.username || errors.email || errors.password || errors.confirmPassword || errors.verificationCode) {
    return
  }

  if (!agreeToTerms.value) {
    globalError.value = '请同意用户协议和隐私政策'
    return
  }

  isLoading.value = true

  try {
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 调用注册API
    const response: ApiResponse<User> = await register({
      username: registerForm.username,
      password: registerForm.password,
      email: registerForm.email,
      verificationCode: registerForm.verificationCode
    })

    if (response.success) {
      // 显示成功提示
      successMessage.value = '注册成功！即将跳转到登录页面...'
      showSuccess.value = true

      // 2秒后跳转到登录页，并传递成功信息
      setTimeout(() => {
        router.push({
          path: '/login',
          query: {
            message: '注册成功！请使用您的邮箱或手机号登录',
            type: 'success'
          }
        })
      }, 2000)
    } else {
      // 显示错误信息
      if (response.message.includes('用户名')) {
        errors.username = response.message
      } else if (response.message.includes('邮箱')) {
        errors.email = response.message
      } else if (response.message.includes('验证码')) {
        errors.verificationCode = response.message
      } else {
        globalError.value = response.message
      }
    }
  } catch (error) {
    globalError.value = '注册失败，请检查网络连接后重试'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* RegisterView 特定样式 - 使用全局CSS变量 */

/* 主容器 */
.register-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 25%, #e8f6f3 50%, #d5f4e6 75%, #fdeaa7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.08), rgba(230, 126, 34, 0.04));
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 400px;
  height: 400px;
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}

.circle-2 {
  width: 250px;
  height: 250px;
  bottom: -125px;
  right: -125px;
  animation-delay: 3s;
}

.circle-3 {
  width: 180px;
  height: 180px;
  top: 30%;
  right: 15%;
  animation-delay: 6s;
}

.wave-pattern {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 1200 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z' fill='%2310b981' fill-opacity='0.03'/%3E%3C/svg%3E") no-repeat;
  background-size: cover;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  33% { transform: translateY(-30px) rotate(120deg) scale(1.05); }
  66% { transform: translateY(15px) rotate(240deg) scale(0.95); }
}

/* 主要内容 */
.register-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1400px;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 700px;
}

/* 表单区域 */
.form-section {
  padding: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  order: 1;
}

.form-container {
  width: 100%;
  max-width: 450px;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--primary-green), var(--secondary-teal), var(--accent-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-header p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* 表单样式 */
.register-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.input-wrapper {
  position: relative;
}

/* 验证码输入框样式 */
.verification-input-wrapper {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.verification-input {
  flex: 1;
  padding: 0.875rem 1rem !important;
  padding-right: 1rem !important;
}

.send-code-btn {
  flex-shrink: 0;
  padding: 0.875rem 1.25rem;
  background: #16a085;
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 120px;
}

.send-code-btn:hover:not(.disabled) {
  background: #138d75 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.3);
}

.send-code-btn.disabled {
  background: var(--border-medium);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  padding-right: 3rem;
  border: 2px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #ffffff;
  box-sizing: border-box;
  color: #1a252f;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-input:hover {
  border-color: #d5e8e3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.form-input:focus {
  outline: none;
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.12), 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.05);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.15);
  border-color: #e74c3c;
}

.input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-light);
  pointer-events: none;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-light);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: var(--text-secondary);
}

/* 隐藏浏览器默认的密码显示切换按钮 */
input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button {
  display: none;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: var(--border-light);
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: var(--error-color);
}

.strength-fill.medium {
  background: var(--warning-color);
}

.strength-fill.strong {
  background: var(--primary-green);
}

.strength-text {
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 2rem;
}

.strength-text.weak {
  color: var(--error-color);
}

.strength-text.medium {
  color: var(--warning-color);
}

.strength-text.strong {
  color: var(--success-color);
}

.error-message {
  display: block;
  color: var(--error-color);
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 68, 68, 0.1);
  border: 1px solid rgba(255, 68, 68, 0.2);
  border-radius: 6px;
  line-height: 1.4;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 复选框样式 */
.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid #2c3e50;
  border-radius: var(--radius-sm);
  margin-right: 0.75rem;
  margin-top: 0.125rem;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
  background: var(--background-white);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.checkbox-wrapper:hover .checkmark {
  border-color: var(--primary-green);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
  background: var(--background-white);
  border-color: #2c3e50;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 3px;
  top: 0px;
  width: 4px;
  height: 8px;
  border: solid #2c3e50;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.terms-link {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.terms-link:hover {
  color: var(--primary-green-dark);
  text-decoration: underline;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #0d5d52 0%, #0a4f47 30%, #085045 70%, #a0522d 100%);
  color: #ffffff;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.register-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  background: linear-gradient(135deg, #0a4f47 0%, #085045 30%, #063d3a 70%, #8b4513 100%);
}

.register-button:hover:not(:disabled)::before {
  left: 100%;
}

.register-button:active:not(:disabled) {
  transform: translateY(0);
}

.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表单底部 */
.form-footer {
  text-align: center;
}

.form-footer p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.login-link {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.login-link:hover {
  color: var(--primary-green-dark);
}

/* 品牌区域 */
.brand-section {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-teal) 30%, var(--primary-green-light) 70%, var(--accent-orange-light) 100%);
  color: white;
  padding: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  order: 2;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Cpath d='M40 40c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm20 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.brand-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.35) 100%);
  pointer-events: none;
}

.brand-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 400px;
}

.brand-logo {
  text-align: center;
  margin-bottom: 3rem;
}

.logo-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  color: white;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.logo-icon svg {
  width: 100%;
  height: 100%;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  color: #ffffff;
  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.5), 0 2px 5px rgba(0, 0, 0, 0.7);
}

/* 优势列表 */
.benefits-list {
  margin-bottom: 3rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
}

.benefit-icon {
  font-size: 2rem;
  margin-right: 1rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.benefit-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.7), 0 1px 3px rgba(0, 0, 0, 0.9);
}

.benefit-content p {
  font-size: 0.875rem;
  margin: 0;
  opacity: 1;
  line-height: 1.5;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* 统计数据 */
.stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  text-align: center;
}

.stat-item {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.9;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .register-content {
    grid-template-columns: 1fr;
    max-width: 600px;
  }

  .form-section {
    order: 1;
  }

  .brand-section {
    order: 2;
    padding: 2rem;
  }

  .brand-title {
    font-size: 2rem;
  }

  .stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .register-container {
    padding: 0.5rem;
  }

  .form-section,
  .brand-section {
    padding: 1.5rem;
  }

  .form-header h2 {
    font-size: 1.875rem;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .benefit-item {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .benefit-icon {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }

  .stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .checkbox-wrapper {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .form-section,
  .brand-section {
    padding: 1rem;
  }

  .form-header h2 {
    font-size: 1.5rem;
  }

  .brand-title {
    font-size: 1.5rem;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }
}

/* 成功提示样式 */
.success-message {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: var(--radius-lg);
  animation: slideInDown 0.5s ease-out;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #155724;
  font-weight: 500;
}

.success-icon {
  width: 24px;
  height: 24px;
  color: #28a745;
  flex-shrink: 0;
}

/* 全局错误提示样式 */
.global-error-message {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.15) 0%, rgba(255, 68, 68, 0.1) 100%);
  border: 1px solid rgba(255, 68, 68, 0.3);
  border-radius: var(--radius-lg);
  animation: slideInDown 0.5s ease-out;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--error-color);
  font-weight: 600;
}

.error-icon {
  width: 24px;
  height: 24px;
  color: var(--error-color);
  flex-shrink: 0;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
