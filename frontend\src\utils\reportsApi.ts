/**
 * 健康报告API工具
 * 提供健康报告相关的API调用方法
 * {{ AURA-X: Modify - 重构为主要调用后端API，减少前端计算. Approval: 寸止. }}
 */

import { authFetch } from './auth'

// 报告时间范围类型
export type ReportPeriod = 'week' | 'month' | 'quarter' | 'year'

// 营养摄入报告数据类型
export interface NutritionReport {
  period: ReportPeriod
  startDate: string
  endDate: string
  totalCalories: number
  averageCalories: number
  dailyCalories: { date: string; calories: number }[]
  macronutrients: {
    protein: number
    fat: number
    carbohydrate: number
    fiber: number
  }
  macronutrientPercentages: {
    protein: number
    fat: number
    carbohydrate: number
  }
  dailyNutrients: {
    date: string
    protein: number
    fat: number
    carbohydrate: number
    fiber: number
  }[]
  recommendations: {
    calories: number
    protein: number
    fat: number
    carbohydrate: number
  }
  complianceScore: number
}

// 健康目标进度报告数据类型
export interface GoalProgressReport {
  period: ReportPeriod
  startDate: string
  endDate: string
  activeGoals: number
  completedGoals: number
  totalGoals: number
  overallProgress: number
  currentStreak: number
  goalDetails: {
    goalId: number
    goalName: string
    goalType: string
    targetValue: number
    currentValue: number
    progress: number
    isCompleted: boolean
    trend: 'up' | 'down' | 'stable'
    historyData: { date: string; value: number; progress: number }[]
  }[]
  achievements: {
    newCompletions: number
    streakRecord: number
    improvementRate: number
  }
}

// 膳食记录分析报告数据类型
export interface MealAnalysisReport {
  period: ReportPeriod
  startDate: string
  endDate: string
  totalMeals: number
  averageMealsPerDay: number
  mealTypeDistribution: {
    breakfast: number
    lunch: number
    dinner: number
    snack: number
  }
  mealFrequency: {
    date: string
    breakfast: boolean
    lunch: boolean
    dinner: boolean
    snack: boolean
    total: number
  }[]
  popularFoods: {
    foodName: string
    frequency: number
    totalCalories: number
  }[]
  eatingPatterns: {
    regularityScore: number
    balanceScore: number
    varietyScore: number
  }
}

// 综合健康评估报告数据类型
export interface HealthAssessmentReport {
  period: ReportPeriod
  startDate: string
  endDate: string
  overallScore: number
  nutritionScore: number
  goalAchievementScore: number
  consistencyScore: number
  improvementAreas: string[]
  strengths: string[]
  recommendations: string[]
  trends: {
    nutrition: 'improving' | 'declining' | 'stable'
    goals: 'improving' | 'declining' | 'stable'
    consistency: 'improving' | 'declining' | 'stable'
  }
  comparisonData: {
    previousPeriod: {
      overallScore: number
      nutritionScore: number
      goalAchievementScore: number
      consistencyScore: number
    }
    improvement: number
  }
}

// 完整报告数据类型
export interface HealthReport {
  nutrition: NutritionReport
  goalProgress: GoalProgressReport
  mealAnalysis: MealAnalysisReport
  healthAssessment: HealthAssessmentReport
  generatedAt: string
}

/**
 * 获取营养推荐摄入量（调用后端API）
 */
async function getNutritionRecommendations(): Promise<{
  calories: number
  protein: number
  fat: number
  carbohydrate: number
}> {
  try {
    const response = await authFetch('/api/health-reports/nutrition-recommendations')
    const data = await response.json()

    if (data.success && data.data) {
      return {
        calories: data.data.calories || 2000,
        protein: data.data.protein || 150,
        fat: data.data.fat || 65,
        carbohydrate: data.data.carbohydrate || 300
      }
    } else {
      throw new Error(data.message || '获取营养推荐失败')
    }
  } catch (error) {
    console.error('获取营养推荐摄入量失败:', error)
    // 返回默认推荐值
    return {
      calories: 2000,
      protein: 150,
      fat: 65,
      carbohydrate: 300
    }
  }
}

/**
 * 获取历史对比数据（调用后端API）
 */
async function getHistoricalComparison(period: ReportPeriod): Promise<{
  previousPeriod: {
    overallScore: number
    nutritionScore: number
    goalAchievementScore: number
    consistencyScore: number
  }
  improvement: number
}> {
  try {
    const response = await authFetch(`/api/health-reports/comparison?period=${period}`)
    const data = await response.json()

    if (data.success && data.data) {
      return data.data
    } else {
      throw new Error(data.message || '获取历史对比数据失败')
    }
  } catch (error) {
    console.error('获取历史对比数据失败:', error)
    // 返回默认对比数据
    return {
      previousPeriod: {
        overallScore: 0,
        nutritionScore: 0,
        goalAchievementScore: 0,
        consistencyScore: 0
      },
      improvement: 0
    }
  }
}

/**
 * 计算日期范围
 */
function getDateRange(period: ReportPeriod): { startDate: string; endDate: string } {
  const endDate = new Date()
  const startDate = new Date()

  switch (period) {
    case 'week':
      startDate.setDate(endDate.getDate() - 7)
      break
    case 'month':
      startDate.setMonth(endDate.getMonth() - 1)
      break
    case 'quarter':
      startDate.setMonth(endDate.getMonth() - 3)
      break
    case 'year':
      startDate.setFullYear(endDate.getFullYear() - 1)
      break
  }

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  }
}

// 注意：以下函数已被后端API替代，保留作为类型定义和备用
// 实际的报告生成现在通过 generateHealthReport() 调用后端API完成

// 这些函数已被后端API替代，现在通过 generateHealthReport() 统一调用后端

// 膳食分析报告生成已移至后端

// 综合健康评估报告生成已移至后端

// 历史报告生成函数已移至后端

// 所有报告生成逻辑已移至后端API

// 前端报告生成逻辑已完全移至后端，现在只保留API调用

/**
 * 生成完整的健康报告（调用后端API）
 */
export async function generateHealthReport(period: ReportPeriod): Promise<HealthReport> {
  try {
    const response = await authFetch(`/api/health-reports/generate?period=${period}`)
    const data = await response.json()

    if (data.success && data.data) {
      // 转换后端数据格式为前端期望的格式
      return {
        nutrition: data.data.nutrition,
        goalProgress: data.data.goalProgress,
        mealAnalysis: data.data.mealAnalysis,
        healthAssessment: data.data.healthAssessment,
        generatedAt: data.data.generatedAt || new Date().toISOString()
      }
    } else {
      throw new Error(data.message || '生成健康报告失败')
    }
  } catch (error) {
    console.error('生成健康报告失败:', error)
    throw new Error('生成健康报告失败')
  }
}
