<template>
  <div class="error-container">
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="nav-actions">
          <router-link to="/" class="nav-btn home-btn">返回首页</router-link>
          <template v-if="isLoggedIn">
            <span class="welcome-text">欢迎，{{ user?.username }}</span>
            <button @click="handleLogout" class="nav-btn logout-btn">退出登录</button>
          </template>
          <template v-else>
            <router-link to="/login" class="nav-btn login-btn">登录</router-link>
          </template>
        </div>
      </div>
    </nav>

    <!-- 错误内容区域 -->
    <main class="error-main">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- 404数字 -->
            <text x="100" y="80" text-anchor="middle" class="error-number">404</text>
            <!-- 搜索图标 -->
            <circle cx="80" cy="130" r="15" stroke="currentColor" stroke-width="3" fill="none"/>
            <line x1="91" y1="141" x2="105" y2="155" stroke="currentColor" stroke-width="3"/>
            <!-- 问号 -->
            <circle cx="130" cy="130" r="20" stroke="currentColor" stroke-width="3" fill="none"/>
            <path d="M125 125 Q130 120 135 125 Q135 130 130 135" stroke="currentColor" stroke-width="2" fill="none"/>
            <circle cx="130" cy="145" r="2" fill="currentColor"/>
          </svg>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">页面未找到</h1>
          <p class="error-description">
            抱歉，您访问的页面不存在或已被移动。<br>
            请检查网址是否正确，或返回首页继续浏览。
          </p>
          
          <!-- 可能的原因 -->
          <div class="error-reasons">
            <h3>可能的原因：</h3>
            <ul>
              <li>网址输入错误</li>
              <li>页面已被删除或移动</li>
              <li>链接已过期</li>
              <li>您没有访问权限</li>
            </ul>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <router-link to="/" class="action-btn primary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回首页
          </router-link>
          
          <button @click="goBack" class="action-btn secondary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回上页
          </button>

          <button @click="refreshPage" class="action-btn tertiary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            刷新页面
          </button>
        </div>

        <!-- 帮助信息 -->
        <div class="help-info">
          <p>如果问题持续存在，请联系我们的技术支持团队</p>
          <a href="mailto:<EMAIL>" class="help-link"><EMAIL></a>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'

const router = useRouter()
const { user, isLoggedIn, logout, updateUserState } = useAuth()

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 处理登出
const handleLogout = () => {
  logout()
  updateUserState()
  router.push('/')
}
</script>

<style scoped>
/* Error404View 特定样式 - 使用全局CSS变量 */

.error-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 顶部导航栏 - 与现有页面保持一致 */
.top-navbar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-btn {
  padding: 0.5rem 1.25rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

.nav-btn.home-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border-color: transparent;
}

.nav-btn.home-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.28);
}

.nav-btn.login-btn {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.nav-btn.login-btn:hover {
  background: var(--primary-color);
  color: white;
}

.nav-btn.logout-btn {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.nav-btn.logout-btn:hover {
  background: var(--accent-color);
  color: white;
}

.welcome-text {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 错误内容区域 */
.error-main {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 100px);
  padding: 2rem;
}

.error-content {
  max-width: 600px;
  text-align: center;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.error-icon {
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
  color: var(--primary-color);
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.error-number {
  font-size: 36px;
  font-weight: 700;
  fill: var(--primary-color);
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.error-reasons {
  text-align: left;
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.error-reasons h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.error-reasons ul {
  list-style: none;
  padding: 0;
}

.error-reasons li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.error-reasons li::before {
  content: '•';
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.btn-icon {
  width: 20px;
  height: 20px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #16a085 0%, #138d75 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.3);
  border: none;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #138d75 0%, #0f7d68 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.4);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  border: none;
}

.action-btn.secondary:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.action-btn.tertiary {
  background: var(--background-light);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.action-btn.tertiary:hover {
  background: var(--background-white);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.help-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.help-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.help-link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }
  
  .nav-btn {
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
  }
  
  .error-main {
    padding: 1rem;
  }
  
  .error-content {
    padding: 2rem 1.5rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
</style>
