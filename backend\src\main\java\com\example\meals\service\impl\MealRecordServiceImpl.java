package com.example.meals.service.impl;

import com.example.meals.dto.MealRecordRequest;
import com.example.meals.dto.MealRecordResponse;
import com.example.meals.entity.MealRecord;
import com.example.meals.entity.MealItem;
import com.example.meals.entity.ChinaFood;
import com.example.meals.mapper.MealRecordMapper;
import com.example.meals.mapper.MealItemMapper;
import com.example.meals.mapper.ChinaFoodMapper;
import com.example.meals.service.MealRecordService;
import com.example.meals.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 膳食记录服务实现类
 * {{ AURA-X: Add - 创建膳食记录服务实现类. Approval: 寸止. }}
 */
@Service
public class MealRecordServiceImpl implements MealRecordService {
    
    @Autowired
    private MealRecordMapper mealRecordMapper;
    
    @Autowired
    private MealItemMapper mealItemMapper;
    
    @Autowired
    private ChinaFoodMapper chinaFoodMapper;
    
    @Override
    @Transactional
    public Result<MealRecordResponse> createMealRecord(Long userId, MealRecordRequest request) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (request.getRecordDate() == null) {
                return Result.badRequest("记录日期不能为空");
            }
            if (request.getMealType() == null || request.getMealType().trim().isEmpty()) {
                return Result.badRequest("膳食类型不能为空");
            }
            
            // 验证膳食类型
            if (!isValidMealType(request.getMealType())) {
                return Result.badRequest("无效的膳食类型");
            }
            
            // 检查是否已存在相同日期和膳食类型的记录
            MealRecord existingRecord = mealRecordMapper.selectByUserIdDateAndMealType(
                    userId, request.getRecordDate(), request.getMealType());
            if (existingRecord != null) {
                return Result.badRequest("该时段的膳食记录已存在，请选择更新操作");
            }
            
            // 创建膳食记录
            MealRecord mealRecord = new MealRecord(userId, request.getRecordDate(), request.getMealType());
            mealRecord.setNotes(request.getNotes());
            
            int result = mealRecordMapper.insert(mealRecord);
            if (result <= 0) {
                return Result.error("创建膳食记录失败");
            }
            
            // 添加膳食条目
            if (request.getMealItems() != null && !request.getMealItems().isEmpty()) {
                for (MealRecordRequest.MealItemRequest itemRequest : request.getMealItems()) {
                    Result<Void> addResult = addMealItem(mealRecord.getId(), itemRequest);
                    if (!addResult.getSuccess()) {
                        return Result.error("添加膳食条目失败：" + addResult.getMessage());
                    }
                }
                
                // 重新计算营养总值
                updateMealRecordNutrition(mealRecord.getId());
            }
            
            // 查询完整的膳食记录信息
            return getMealRecordById(userId, mealRecord.getId());
            
        } catch (Exception e) {
            return Result.error("创建膳食记录失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public Result<MealRecordResponse> updateMealRecord(Long userId, Long recordId, MealRecordRequest request) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordId == null || recordId <= 0) {
                return Result.badRequest("膳食记录ID不能为空");
            }
            
            // 检查记录是否存在且属于当前用户
            MealRecord existingRecord = mealRecordMapper.selectById(recordId);
            if (existingRecord == null) {
                return Result.notFound("膳食记录不存在");
            }
            if (!existingRecord.getUserId().equals(userId)) {
                return Result.forbidden("无权限操作此膳食记录");
            }
            
            // 更新基本信息
            if (request.getMealType() != null && !request.getMealType().trim().isEmpty()) {
                if (!isValidMealType(request.getMealType())) {
                    return Result.badRequest("无效的膳食类型");
                }
                existingRecord.setMealType(request.getMealType());
            }
            if (request.getNotes() != null) {
                existingRecord.setNotes(request.getNotes());
            }
            
            // 更新膳食条目
            if (request.getMealItems() != null) {
                // 删除现有条目
                mealItemMapper.deleteByMealRecordId(recordId);
                
                // 添加新条目
                for (MealRecordRequest.MealItemRequest itemRequest : request.getMealItems()) {
                    Result<Void> addResult = addMealItem(recordId, itemRequest);
                    if (!addResult.getSuccess()) {
                        return Result.error("更新膳食条目失败：" + addResult.getMessage());
                    }
                }
            }
            
            // 重新计算营养总值
            updateMealRecordNutrition(recordId);
            
            // 更新膳食记录
            mealRecordMapper.update(existingRecord);
            
            // 返回更新后的记录
            return getMealRecordById(userId, recordId);
            
        } catch (Exception e) {
            return Result.error("更新膳食记录失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public Result<Void> deleteMealRecord(Long userId, Long recordId) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordId == null || recordId <= 0) {
                return Result.badRequest("膳食记录ID不能为空");
            }
            
            // 检查记录是否存在且属于当前用户
            MealRecord existingRecord = mealRecordMapper.selectById(recordId);
            if (existingRecord == null) {
                return Result.notFound("膳食记录不存在");
            }
            if (!existingRecord.getUserId().equals(userId)) {
                return Result.forbidden("无权限操作此膳食记录");
            }
            
            // 删除膳食条目（由于外键约束，会自动删除）
            mealItemMapper.deleteByMealRecordId(recordId);
            
            // 删除膳食记录
            int result = mealRecordMapper.deleteById(recordId);
            if (result <= 0) {
                return Result.error("删除膳食记录失败");
            }
            
            return Result.success();
            
        } catch (Exception e) {
            return Result.error("删除膳食记录失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<MealRecordResponse> getMealRecordById(Long userId, Long recordId) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordId == null || recordId <= 0) {
                return Result.badRequest("膳食记录ID不能为空");
            }
            
            // 查询膳食记录
            MealRecord mealRecord = mealRecordMapper.selectById(recordId);
            if (mealRecord == null) {
                return Result.notFound("膳食记录不存在");
            }
            if (!mealRecord.getUserId().equals(userId)) {
                return Result.forbidden("无权限访问此膳食记录");
            }
            
            // 查询膳食条目
            List<MealItem> mealItems = mealItemMapper.selectByMealRecordId(recordId);
            mealRecord.setMealItems(mealItems);
            
            return Result.success(new MealRecordResponse(mealRecord));
            
        } catch (Exception e) {
            return Result.error("获取膳食记录失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<MealRecordResponse>> getMealRecordsByDate(Long userId, LocalDate recordDate) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordDate == null) {
                return Result.badRequest("记录日期不能为空");
            }
            
            // 查询膳食记录
            List<MealRecord> mealRecords = mealRecordMapper.selectByUserIdAndDate(userId, recordDate);
            
            // 批量查询膳食条目
            if (!mealRecords.isEmpty()) {
                List<Long> recordIds = mealRecords.stream()
                        .map(MealRecord::getId)
                        .collect(Collectors.toList());
                List<MealItem> allMealItems = mealItemMapper.selectByMealRecordIds(recordIds);
                
                // 按膳食记录ID分组
                Map<Long, List<MealItem>> itemsMap = allMealItems.stream()
                        .collect(Collectors.groupingBy(MealItem::getMealRecordId));
                
                // 设置膳食条目
                mealRecords.forEach(record -> 
                    record.setMealItems(itemsMap.getOrDefault(record.getId(), List.of())));
            }
            
            List<MealRecordResponse> responses = mealRecords.stream()
                    .map(MealRecordResponse::new)
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("获取膳食记录失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<MealRecordResponse>> getMealRecordsByDateRange(Long userId, LocalDate startDate, LocalDate endDate) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (startDate == null || endDate == null) {
                return Result.badRequest("日期范围不能为空");
            }
            if (startDate.isAfter(endDate)) {
                return Result.badRequest("开始日期不能晚于结束日期");
            }

            // 查询膳食记录
            List<MealRecord> mealRecords = mealRecordMapper.selectByUserIdAndDateRange(userId, startDate, endDate);

            // 批量查询膳食条目
            if (!mealRecords.isEmpty()) {
                List<Long> recordIds = mealRecords.stream()
                        .map(MealRecord::getId)
                        .collect(Collectors.toList());
                List<MealItem> allMealItems = mealItemMapper.selectByMealRecordIds(recordIds);

                // 按膳食记录ID分组
                Map<Long, List<MealItem>> itemsMap = allMealItems.stream()
                        .collect(Collectors.groupingBy(MealItem::getMealRecordId));

                // 设置膳食条目
                mealRecords.forEach(record ->
                    record.setMealItems(itemsMap.getOrDefault(record.getId(), List.of())));
            }

            List<MealRecordResponse> responses = mealRecords.stream()
                    .map(MealRecordResponse::new)
                    .collect(Collectors.toList());

            return Result.success(responses);

        } catch (Exception e) {
            return Result.error("获取膳食记录失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<MealRecordResponse>> getRecentMealRecords(Long userId, Integer limit) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (limit == null || limit <= 0) {
                limit = 10; // 默认10条
            }
            if (limit > 100) {
                limit = 100; // 最大100条
            }

            // 查询最近的膳食记录
            List<MealRecord> mealRecords = mealRecordMapper.selectRecentByUserId(userId, limit);

            // 批量查询膳食条目
            if (!mealRecords.isEmpty()) {
                List<Long> recordIds = mealRecords.stream()
                        .map(MealRecord::getId)
                        .collect(Collectors.toList());
                List<MealItem> allMealItems = mealItemMapper.selectByMealRecordIds(recordIds);

                // 按膳食记录ID分组
                Map<Long, List<MealItem>> itemsMap = allMealItems.stream()
                        .collect(Collectors.groupingBy(MealItem::getMealRecordId));

                // 设置膳食条目
                mealRecords.forEach(record ->
                    record.setMealItems(itemsMap.getOrDefault(record.getId(), List.of())));
            }

            List<MealRecordResponse> responses = mealRecords.stream()
                    .map(MealRecordResponse::new)
                    .collect(Collectors.toList());

            return Result.success(responses);

        } catch (Exception e) {
            return Result.error("获取最近膳食记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<MealRecordResponse> addFoodToMealRecord(Long userId, Long recordId, Long foodId, BigDecimal weight) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordId == null || recordId <= 0) {
                return Result.badRequest("膳食记录ID不能为空");
            }
            if (foodId == null || foodId <= 0) {
                return Result.badRequest("食物ID不能为空");
            }
            if (weight == null || weight.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.badRequest("重量必须大于0");
            }
            if (weight.compareTo(new BigDecimal("10000")) > 0) {
                return Result.badRequest("重量不能超过10000克");
            }

            // 检查膳食记录是否存在且属于当前用户
            MealRecord mealRecord = mealRecordMapper.selectById(recordId);
            if (mealRecord == null) {
                return Result.notFound("膳食记录不存在");
            }
            if (!mealRecord.getUserId().equals(userId)) {
                return Result.forbidden("无权限操作此膳食记录");
            }

            // 创建膳食条目请求
            MealRecordRequest.MealItemRequest itemRequest = new MealRecordRequest.MealItemRequest(foodId, weight);

            // 添加膳食条目
            Result<Void> addResult = addMealItem(recordId, itemRequest);
            if (!addResult.getSuccess()) {
                return Result.error("添加食物失败：" + addResult.getMessage());
            }

            // 重新计算营养总值
            updateMealRecordNutrition(recordId);

            // 返回更新后的膳食记录
            return getMealRecordById(userId, recordId);

        } catch (Exception e) {
            return Result.error("添加食物到膳食记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<MealRecordResponse> removeFoodFromMealRecord(Long userId, Long recordId, Long itemId) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordId == null || recordId <= 0) {
                return Result.badRequest("膳食记录ID不能为空");
            }
            if (itemId == null || itemId <= 0) {
                return Result.badRequest("膳食条目ID不能为空");
            }

            // 检查膳食记录是否存在且属于当前用户
            MealRecord mealRecord = mealRecordMapper.selectById(recordId);
            if (mealRecord == null) {
                return Result.notFound("膳食记录不存在");
            }
            if (!mealRecord.getUserId().equals(userId)) {
                return Result.forbidden("无权限操作此膳食记录");
            }

            // 检查膳食条目是否存在且属于该膳食记录
            MealItem mealItem = mealItemMapper.selectById(itemId);
            if (mealItem == null) {
                return Result.notFound("膳食条目不存在");
            }
            if (!mealItem.getMealRecordId().equals(recordId)) {
                return Result.badRequest("膳食条目不属于该膳食记录");
            }

            // 删除膳食条目
            int result = mealItemMapper.deleteById(itemId);
            if (result <= 0) {
                return Result.error("删除膳食条目失败");
            }

            // 重新计算营养总值
            updateMealRecordNutrition(recordId);

            // 返回更新后的膳食记录
            return getMealRecordById(userId, recordId);

        } catch (Exception e) {
            return Result.error("从膳食记录移除食物失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<MealRecordResponse> updateMealItemWeight(Long userId, Long recordId, Long itemId, BigDecimal weight) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (recordId == null || recordId <= 0) {
                return Result.badRequest("膳食记录ID不能为空");
            }
            if (itemId == null || itemId <= 0) {
                return Result.badRequest("膳食条目ID不能为空");
            }
            if (weight == null || weight.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.badRequest("重量必须大于0");
            }
            if (weight.compareTo(new BigDecimal("10000")) > 0) {
                return Result.badRequest("重量不能超过10000克");
            }

            // 检查膳食记录是否存在且属于当前用户
            MealRecord mealRecord = mealRecordMapper.selectById(recordId);
            if (mealRecord == null) {
                return Result.notFound("膳食记录不存在");
            }
            if (!mealRecord.getUserId().equals(userId)) {
                return Result.forbidden("无权限操作此膳食记录");
            }

            // 检查膳食条目是否存在且属于该膳食记录
            MealItem mealItem = mealItemMapper.selectById(itemId);
            if (mealItem == null) {
                return Result.notFound("膳食条目不存在");
            }
            if (!mealItem.getMealRecordId().equals(recordId)) {
                return Result.badRequest("膳食条目不属于该膳食记录");
            }

            // 获取食物信息重新计算营养值
            ChinaFood food = chinaFoodMapper.getFoodById(mealItem.getFoodId());
            if (food == null) {
                return Result.error("食物信息不存在");
            }

            // 更新膳食条目
            mealItem.setWeight(weight);
            calculateMealItemNutrition(mealItem, food, weight);

            int result = mealItemMapper.update(mealItem);
            if (result <= 0) {
                return Result.error("更新膳食条目失败");
            }

            // 重新计算营养总值
            updateMealRecordNutrition(recordId);

            // 返回更新后的膳食记录
            return getMealRecordById(userId, recordId);

        } catch (Exception e) {
            return Result.error("更新膳食条目重量失败：" + e.getMessage());
        }
    }

    @Override
    public Result<MealRecordStatistics> getMealRecordStatistics(Long userId, LocalDate startDate, LocalDate endDate) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.badRequest("用户ID不能为空");
            }
            if (startDate == null || endDate == null) {
                return Result.badRequest("日期范围不能为空");
            }
            if (startDate.isAfter(endDate)) {
                return Result.badRequest("开始日期不能晚于结束日期");
            }

            // 查询膳食记录
            List<MealRecord> mealRecords = mealRecordMapper.selectByUserIdAndDateRange(userId, startDate, endDate);

            MealRecordStatistics statistics = new MealRecordStatistics();
            statistics.setTotalRecords(mealRecords.size());

            if (mealRecords.isEmpty()) {
                statistics.setTotalDays(0);
                statistics.setTotalCalories(BigDecimal.ZERO);
                statistics.setAverageCalories(BigDecimal.ZERO);
                statistics.setTotalProtein(BigDecimal.ZERO);
                statistics.setTotalFat(BigDecimal.ZERO);
                statistics.setTotalCarbohydrate(BigDecimal.ZERO);
                statistics.setTotalDietaryFiber(BigDecimal.ZERO);
                statistics.setAvgDailyEnergy(BigDecimal.ZERO);
                statistics.setAvgDailyProtein(BigDecimal.ZERO);
                statistics.setAvgDailyFat(BigDecimal.ZERO);
                statistics.setAvgDailyCarbohydrate(BigDecimal.ZERO);
                statistics.setAvgDailyDietaryFiber(BigDecimal.ZERO);
                return Result.success(statistics);
            }

            // 按日期分组统计
            Map<LocalDate, List<MealRecord>> dailyRecords = mealRecords.stream()
                    .collect(Collectors.groupingBy(MealRecord::getRecordDate));

            statistics.setTotalDays(dailyRecords.size());

            // 计算总营养素
            BigDecimal totalEnergy = mealRecords.stream()
                    .map(MealRecord::getTotalEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalProtein = mealRecords.stream()
                    .map(MealRecord::getTotalProtein)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalFat = mealRecords.stream()
                    .map(MealRecord::getTotalFat)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalCarbohydrate = mealRecords.stream()
                    .map(MealRecord::getTotalCarbohydrate)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalDietaryFiber = mealRecords.stream()
                    .map(MealRecord::getTotalDietaryFiber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 设置总营养素
            statistics.setTotalCalories(totalEnergy);
            statistics.setTotalProtein(totalProtein);
            statistics.setTotalFat(totalFat);
            statistics.setTotalCarbohydrate(totalCarbohydrate);
            statistics.setTotalDietaryFiber(totalDietaryFiber);

            // 计算每日营养总值
            BigDecimal totalDailyEnergy = BigDecimal.ZERO;
            BigDecimal totalDailyProtein = BigDecimal.ZERO;
            BigDecimal totalDailyFat = BigDecimal.ZERO;
            BigDecimal totalDailyCarbohydrate = BigDecimal.ZERO;
            BigDecimal totalDailyDietaryFiber = BigDecimal.ZERO;

            for (List<MealRecord> dayRecords : dailyRecords.values()) {
                BigDecimal dayEnergy = dayRecords.stream()
                        .map(MealRecord::getTotalEnergy)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal dayProtein = dayRecords.stream()
                        .map(MealRecord::getTotalProtein)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal dayFat = dayRecords.stream()
                        .map(MealRecord::getTotalFat)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal dayCarbohydrate = dayRecords.stream()
                        .map(MealRecord::getTotalCarbohydrate)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal dayDietaryFiber = dayRecords.stream()
                        .map(MealRecord::getTotalDietaryFiber)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                totalDailyEnergy = totalDailyEnergy.add(dayEnergy);
                totalDailyProtein = totalDailyProtein.add(dayProtein);
                totalDailyFat = totalDailyFat.add(dayFat);
                totalDailyCarbohydrate = totalDailyCarbohydrate.add(dayCarbohydrate);
                totalDailyDietaryFiber = totalDailyDietaryFiber.add(dayDietaryFiber);
            }

            // 计算平均值
            BigDecimal dayCount = new BigDecimal(statistics.getTotalDays());
            statistics.setAverageCalories(totalDailyEnergy.divide(dayCount, 2, RoundingMode.HALF_UP));
            statistics.setAvgDailyEnergy(totalDailyEnergy.divide(dayCount, 2, RoundingMode.HALF_UP));
            statistics.setAvgDailyProtein(totalDailyProtein.divide(dayCount, 2, RoundingMode.HALF_UP));
            statistics.setAvgDailyFat(totalDailyFat.divide(dayCount, 2, RoundingMode.HALF_UP));
            statistics.setAvgDailyCarbohydrate(totalDailyCarbohydrate.divide(dayCount, 2, RoundingMode.HALF_UP));
            statistics.setAvgDailyDietaryFiber(totalDailyDietaryFiber.divide(dayCount, 2, RoundingMode.HALF_UP));

            // 计算膳食类型分布
            MealRecordService.MealTypeDistribution distribution = statistics.getMealTypeDistribution();
            for (MealRecord record : mealRecords) {
                distribution.incrementMealType(record.getMealType());
            }

            return Result.success(statistics);

        } catch (Exception e) {
            return Result.error("获取膳食记录统计失败：" + e.getMessage());
        }
    }

    /**
     * 验证膳食类型是否有效
     */
    private boolean isValidMealType(String mealType) {
        return "breakfast".equals(mealType) || "lunch".equals(mealType) ||
               "dinner".equals(mealType) || "snack".equals(mealType);
    }

    /**
     * 添加膳食条目
     */
    private Result<Void> addMealItem(Long mealRecordId, MealRecordRequest.MealItemRequest itemRequest) {
        try {
            // 验证食物是否存在
            ChinaFood food = chinaFoodMapper.getFoodById(itemRequest.getFoodId());
            if (food == null) {
                return Result.badRequest("食物不存在");
            }

            // 创建膳食条目
            MealItem mealItem = new MealItem(mealRecordId, itemRequest.getFoodId(),
                    food.getFoodName(), food.getFoodCode(), itemRequest.getWeight());

            // 计算营养值
            calculateMealItemNutrition(mealItem, food, itemRequest.getWeight());

            // 插入数据库
            int result = mealItemMapper.insert(mealItem);
            if (result <= 0) {
                return Result.error("添加膳食条目失败");
            }

            return Result.success();

        } catch (Exception e) {
            return Result.error("添加膳食条目失败：" + e.getMessage());
        }
    }

    /**
     * 计算膳食条目的营养值
     */
    private void calculateMealItemNutrition(MealItem mealItem, ChinaFood food, BigDecimal weight) {
        // 计算系数（实际重量/100g）
        BigDecimal factor = weight.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

        // 计算各营养成分
        mealItem.setEnergy(calculateNutrientValue(food.getEnergyKcal(), factor));
        mealItem.setProtein(calculateNutrientValue(food.getProtein(), factor));
        mealItem.setFat(calculateNutrientValue(food.getFat(), factor));
        mealItem.setCarbohydrate(calculateNutrientValue(food.getCho(), factor));
        mealItem.setDietaryFiber(calculateNutrientValue(food.getDietaryFiber(), factor));
    }

    /**
     * 计算营养成分值
     */
    private BigDecimal calculateNutrientValue(Object nutrientPer100g, BigDecimal factor) {
        if (nutrientPer100g == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal value;
        if (nutrientPer100g instanceof Integer) {
            value = new BigDecimal((Integer) nutrientPer100g);
        } else if (nutrientPer100g instanceof BigDecimal) {
            value = (BigDecimal) nutrientPer100g;
        } else {
            return BigDecimal.ZERO;
        }

        return value.multiply(factor).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 更新膳食记录的营养总值
     */
    private void updateMealRecordNutrition(Long mealRecordId) {
        try {
            // 计算营养总值
            Map<String, BigDecimal> totals = mealItemMapper.calculateNutritionTotals(mealRecordId);

            // 更新膳食记录
            mealRecordMapper.updateNutritionTotals(
                    mealRecordId,
                    totals.getOrDefault("totalEnergy", BigDecimal.ZERO),
                    totals.getOrDefault("totalProtein", BigDecimal.ZERO),
                    totals.getOrDefault("totalFat", BigDecimal.ZERO),
                    totals.getOrDefault("totalCarbohydrate", BigDecimal.ZERO),
                    totals.getOrDefault("totalDietaryFiber", BigDecimal.ZERO)
            );
        } catch (Exception e) {
            // 记录错误但不抛出异常，避免影响主流程
            System.err.println("更新膳食记录营养总值失败：" + e.getMessage());
        }
    }
}
