<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主要内容 -->
    <div class="login-content">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-logo">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h1 class="brand-title">膳食营养分析平台</h1>
        </div>
        <p class="brand-subtitle">科学饮食，健康生活</p>
        <div class="feature-list">
          <div class="feature-item">
            <span class="feature-icon">🥗</span>
            <span>智能营养分析</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📊</span>
            <span>个性化建议</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🎯</span>
            <span>健康目标管理</span>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>欢迎回来</h2>
            <p>登录您的账户，继续您的健康之旅</p>
          </div>

          <!-- 成功提示 -->
          <div v-if="showSuccess" class="success-message">
            <div class="success-content">
              <svg class="success-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>{{ successMessage }}</span>
            </div>
          </div>

          <!-- 全局错误提示 -->
          <div v-if="globalError" class="global-error-message">
            <div class="error-content">
              <svg class="error-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ globalError }}</span>
            </div>
          </div>

          <!-- 登录方式切换 -->
          <div class="login-mode-switch">
            <button
              type="button"
              class="mode-button"
              @click="switchLoginMode"
              v-if="loginMode === 'code'"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L15.09 8.26L22 9L15.09 9.74L12 17L8.91 9.74L2 9L8.91 8.26L12 1Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              使用密码登录
            </button>
            <button
              type="button"
              class="mode-button"
              @click="switchLoginMode"
              v-if="loginMode === 'password'"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              使用验证码登录
            </button>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <!-- 密码登录表单 -->
            <template v-if="loginMode === 'password'">
              <div class="form-group">
              <label for="emailOrPhone" class="form-label">邮箱地址</label>
              <div class="input-wrapper">
                <input
                  id="emailOrPhone"
                  v-model="loginForm.emailOrPhone"
                  type="text"
                  class="form-input"
                  :class="{ 'error': errors.emailOrPhone }"
                  placeholder="请输入邮箱地址"
                  @blur="validateEmailOrPhone"
                />
                <span class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </span>
              </div>
              <span v-if="errors.emailOrPhone" class="error-message">{{ errors.emailOrPhone }}</span>
            </div>

            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="input-wrapper">
                <input
                  id="password"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.password }"
                  placeholder="请输入您的密码"
                  @blur="validatePassword"
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.584 10.587A2 2 0 0 0 13.415 13.414" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
              <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
            </div>
            </template>

            <!-- 验证码登录表单 -->
            <template v-else>
              <div class="form-group">
                <label for="email" class="form-label">邮箱地址</label>
                <div class="input-wrapper">
                  <input
                    id="email"
                    v-model="emailCodeForm.email"
                    type="email"
                    class="form-input"
                    :class="{ 'error': errors.email }"
                    placeholder="请输入邮箱地址"
                    @blur="validateEmail"
                  />
                  <span class="input-icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </span>
                </div>
                <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
              </div>

              <div class="form-group">
                <label for="verificationCode" class="form-label">验证码</label>
                <div class="verification-input-wrapper">
                  <input
                    id="verificationCode"
                    v-model="emailCodeForm.verificationCode"
                    type="text"
                    class="form-input verification-input"
                    :class="{ 'error': errors.verificationCode }"
                    placeholder="请输入6位验证码"
                    maxlength="6"
                    @blur="validateVerificationCode"
                  />
                  <button
                    type="button"
                    class="send-code-button"
                    :disabled="isCodeLoading || countdown > 0 || !emailCodeForm.email"
                    @click="sendCode"
                  >
                    <span v-if="isCodeLoading" class="loading-spinner"></span>
                    <span v-else-if="countdown > 0">{{ countdown }}s</span>
                    <span v-else>发送验证码</span>
                  </button>
                </div>
                <span v-if="errors.verificationCode" class="error-message">{{ errors.verificationCode }}</span>
              </div>
            </template>

            <div class="form-options">
              <label class="checkbox-wrapper" title="勾选后，关闭浏览器仍保持登录状态30天">
                <input type="checkbox" v-model="rememberMe" />
                <span class="checkmark"></span>
                <span class="checkbox-label">记住我</span>
              </label>
              <router-link to="/forgot-password" class="forgot-password">忘记密码？</router-link>
            </div>

            <button type="submit" class="login-button" :disabled="isLoading">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span>{{ isLoading ? '登录中...' : '登录' }}</span>
            </button>
          </form>

          <div class="form-footer">
            <p>还没有账户？ 
              <router-link to="/register" class="register-link">立即注册</router-link>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import { useUserStore } from '../stores/user'
import { login, loginWithEmailCode, sendVerificationCode, type LoginRequest, type EmailCodeLoginRequest, type ApiResponse } from '../utils/userApi'
import { useAuth } from '../composables/useAuth'
import { AuthManager } from '../utils/auth'

// 类型定义
interface LoginForm {
  emailOrPhone: string
  password: string
}

interface EmailCodeForm {
  email: string
  verificationCode: string
}

interface FormErrors {
  emailOrPhone?: string
  password?: string
  email?: string
  verificationCode?: string
}

// 响应式数据
const router = useRouter()
const route = useRoute()
const { setUser, updateUserState } = useAuth()
// const userStore = useUserStore()
const showPassword = ref(false)
const rememberMe = ref(false)
const successMessage = ref('')
const showSuccess = ref(false)
const isLoading = ref(false)
const globalError = ref('')

// 登录方式切换
const loginMode = ref<'password' | 'code'>('password')
const isCodeLoading = ref(false)
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)

const loginForm = reactive<LoginForm>({
  emailOrPhone: '',
  password: ''
})

const emailCodeForm = reactive<EmailCodeForm>({
  email: '',
  verificationCode: ''
})

const errors = reactive<FormErrors>({})

// 验证方法
const validateEmailOrPhone = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  if (!loginForm.emailOrPhone) {
    errors.emailOrPhone = '请输入邮箱地址'
  } else if (!emailRegex.test(loginForm.emailOrPhone)) {
    errors.emailOrPhone = '请输入有效的邮箱地址'
  } else {
    errors.emailOrPhone = undefined
  }
}

const validatePassword = () => {
  if (!loginForm.password) {
    errors.password = '请输入密码'
  } else if (loginForm.password.length < 6) {
    errors.password = '密码长度至少6位'
  } else {
    errors.password = undefined
  }
}

// 验证邮箱（验证码登录）
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  if (!emailCodeForm.email) {
    errors.email = '请输入邮箱地址'
  } else if (!emailRegex.test(emailCodeForm.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = undefined
  }
}

// 验证验证码
const validateVerificationCode = () => {
  if (!emailCodeForm.verificationCode) {
    errors.verificationCode = '请输入验证码'
  } else if (emailCodeForm.verificationCode.length !== 6) {
    errors.verificationCode = '验证码为6位数字'
  } else {
    errors.verificationCode = undefined
  }
}

// 发送验证码
const sendCode = async () => {
  validateEmail()
  if (errors.email) {
    return
  }

  isCodeLoading.value = true
  try {
    const response = await sendVerificationCode(emailCodeForm.email, 'LOGIN')
    if (response.success) {
      // 开始倒计时
      countdown.value = 60
      countdownTimer.value = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(countdownTimer.value!)
          countdownTimer.value = null
        }
      }, 1000)
    } else {
      globalError.value = response.message
    }
  } catch (error: any) {
    globalError.value = error.message || '发送验证码失败'
  } finally {
    isCodeLoading.value = false
  }
}

// 切换登录方式
const switchLoginMode = () => {
  loginMode.value = loginMode.value === 'password' ? 'code' : 'password'
  globalError.value = ''
  // 清除表单错误
  Object.keys(errors).forEach(key => {
    delete errors[key as keyof FormErrors]
  })
}

// 登录处理
const handleLogin = async () => {
  // 清除之前的全局错误
  globalError.value = ''

  if (loginMode.value === 'password') {
    // 密码登录验证
    validateEmailOrPhone()
    validatePassword()

    if (errors.emailOrPhone || errors.password) {
      return
    }
  } else {
    // 验证码登录验证
    validateEmail()
    validateVerificationCode()

    if (errors.email || errors.verificationCode) {
      return
    }
  }

  isLoading.value = true

  try {
    let response: ApiResponse<any>

    if (loginMode.value === 'password') {
      // 密码登录
      response = await login({
        emailOrPhone: loginForm.emailOrPhone,
        password: loginForm.password,
        rememberMe: rememberMe.value
      })
    } else {
      // 验证码登录
      response = await loginWithEmailCode({
        email: emailCodeForm.email,
        verificationCode: emailCodeForm.verificationCode,
        rememberMe: rememberMe.value
      })
    }

    if (response.success && response.data) {
      // 使用AuthManager正确设置用户状态
      const { token, ...userData } = response.data
      if (token) {
        AuthManager.login(token, { ...userData, userType: 'USER' }, rememberMe.value)
        // 立即更新全局状态
        updateUserState()
      }

      // 增加延迟确保状态更新完成后再跳转
      await new Promise(resolve => setTimeout(resolve, 200))

      // 检查是否有重定向路径
      const redirectPath = route.query.redirect as string
      if (redirectPath) {
        router.push(redirectPath)
      } else {
        router.push('/')  // 登录成功后跳转到首页
      }
    } else {
      globalError.value = response.message
    }
  } catch (error: any) {
    globalError.value = error.message || '登录失败，请检查网络连接后重试'
  } finally {
    isLoading.value = false
  }
}

// 处理从注册页面传递过来的成功消息
onMounted(() => {
  const message = route.query.message as string
  const type = route.query.type as string

  if (message && type === 'success') {
    successMessage.value = message
    showSuccess.value = true

    // 5秒后自动隐藏成功消息
    setTimeout(() => {
      showSuccess.value = false
    }, 5000)

    // 清除URL中的查询参数
    router.replace({ path: '/login' })
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
/* LoginView 特定样式 - 使用全局CSS变量 */

/* 主容器 */
.login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 30%, #e8f6f3 70%, #d5f4e6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.08), rgba(72, 201, 176, 0.04));
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容 */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

/* 品牌区域 */
.brand-section {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-teal) 50%, var(--primary-green-light) 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.2;
}

.brand-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.15) 50%, rgba(0, 0, 0, 0.3) 100%);
  pointer-events: none;
}

.brand-logo {
  position: relative;
  z-index: 1;
  margin-bottom: 2rem;
}

.logo-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 1rem;
  color: white;
}

.logo-icon svg {
  width: 100%;
  height: 100%;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.5);
}

.brand-subtitle {
  font-size: 1.125rem;
  opacity: 1;
  margin: 0 0 3rem 0;
  font-weight: 400;
  color: #ffffff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6), 0 1px 3px rgba(0, 0, 0, 0.8);
}

.feature-list {
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 1rem;
  opacity: 1;
  color: #ffffff;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.9);
}

.feature-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 2rem;
  text-align: center;
}

/* 表单区域 */
.form-section {
  padding: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.form-header p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* 登录方式切换 */
.login-mode-switch {
  margin-bottom: 1.5rem;
  text-align: center;
}

.mode-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--background-light);
  color: var(--text-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-button:hover {
  background: #16a085 !important;
  color: #fcfcfc !important;
  border-color: #16a085 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(22, 160, 133, 0.3);
}

.mode-button svg {
  width: 1rem;
  height: 1rem;
}

/* 表单样式 */
.login-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  padding-right: 3rem;
  border: 2px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #ffffff;
  box-sizing: border-box;
  color: #1a252f;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-input:hover {
  border-color: #d5e8e3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.form-input:focus {
  outline: none;
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.12), 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.05);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.15);
  border-color: #e74c3c;
}

.input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-light);
  pointer-events: none;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-light);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: var(--text-secondary);
}

/* 隐藏浏览器默认的密码显示切换按钮 */
input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button {
  display: none;
}

.error-message {
  display: block;
  color: var(--error-color);
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 68, 68, 0.1);
  border: 1px solid rgba(255, 68, 68, 0.2);
  border-radius: 6px;
  line-height: 1.4;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 验证码输入框样式 */
.verification-input-wrapper {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.verification-input {
  flex: 1;
}

.send-code-button {
  padding: 0.75rem 1rem;
  background: var(--background-light);
  color: var(--text-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.send-code-button:hover:not(:disabled) {
  background: #16a085 !important;
  color: #fdfeff !important;
  border-color: #16a085 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(22, 160, 133, 0.3);
}

.send-code-button:disabled {
  background: var(--border-light);
  color: var(--text-muted);
  cursor: not-allowed;
}

.send-code-button .loading-spinner {
  width: 0.875rem;
  height: 0.875rem;
  border: 1.5px solid rgba(0, 0, 0, 0.2);
  border-top: 1.5px solid var(--text-secondary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 1rem;
  height: 1rem;
  border: 2px solid #2c3e50;
  border-radius: var(--radius-sm);
  margin-right: 0.5rem;
  position: relative;
  transition: all 0.2s ease;
  background: var(--background-white);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.checkbox-wrapper:hover .checkmark {
  border-color: var(--primary-green);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
  background: var(--background-white);
  border-color: #2c3e50;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 3px;
  top: 0px;
  width: 4px;
  height: 8px;
  border: solid #2c3e50;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.forgot-password {
  color: var(--primary-green);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: var(--primary-green-dark);
}

/* 登录按钮 */
.login-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #0d5d52 0%, #0a4f47 50%, #085045 100%);
  color: #ffffff;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, #0a4f47 0%, #085045 50%, #063d3a 100%);
}

.login-button:hover:not(:disabled)::before {
  left: 100%;
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表单底部 */
.form-footer {
  text-align: center;
}

.form-footer p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.register-link {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.register-link:hover {
  color: var(--primary-green-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .brand-section {
    padding: 2rem;
    text-align: center;
  }

  .brand-title {
    font-size: 2rem;
  }

  .form-section {
    padding: 2rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 0.5rem;
  }

  .brand-section,
  .form-section {
    padding: 1.5rem;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .form-header h2 {
    font-size: 1.75rem;
  }
}

/* 成功提示样式 */
.success-message {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: var(--radius-lg);
  animation: slideInDown 0.5s ease-out;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #155724;
  font-weight: 500;
}

.success-icon {
  width: 24px;
  height: 24px;
  color: #28a745;
  flex-shrink: 0;
}

/* 全局错误提示样式 */
.global-error-message {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.15) 0%, rgba(255, 68, 68, 0.1) 100%);
  border: 1px solid rgba(255, 68, 68, 0.3);
  border-radius: var(--radius-lg);
  animation: slideInDown 0.5s ease-out;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--error-color);
  font-weight: 600;
}

.error-icon {
  width: 24px;
  height: 24px;
  color: var(--error-color);
  flex-shrink: 0;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
