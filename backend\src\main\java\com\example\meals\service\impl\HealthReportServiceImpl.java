package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthReportResponse;
import com.example.meals.dto.NutritionRecommendationResponse;
import com.example.meals.dto.UserResponse;
import com.example.meals.dto.UserGoalStatsResponse;
import com.example.meals.dto.UserGoalResponse;
import com.example.meals.service.HealthReportService;
import com.example.meals.service.MealRecordService;
import com.example.meals.service.HealthGoalService;
import com.example.meals.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 健康报告服务实现类
 * {{ AURA-X: Add - 实现健康报告服务，提供完整的报告生成逻辑. Approval: 寸止. }}
 */
@Service
public class HealthReportServiceImpl implements HealthReportService {

    @Autowired
    private MealRecordService mealRecordService;
    
    @Autowired
    private HealthGoalService healthGoalService;
    
    @Autowired
    private UserService userService;

    @Override
    public Result<HealthReportResponse> generateHealthReport(Long userId, String period) {
        try {
            // 计算日期范围
            DateRange dateRange = calculateDateRange(period);

            // 生成报告
            HealthReportResponse report = new HealthReportResponse();
            report.setPeriod(period);
            report.setStartDate(dateRange.startDate);
            report.setEndDate(dateRange.endDate);
            report.setGeneratedAt(LocalDateTime.now());

            // 生成各项报告数据
            report.setNutrition(generateNutritionReport(userId, dateRange));
            report.setGoalProgress(generateGoalProgressReport(userId, dateRange));
            report.setMealAnalysis(generateMealAnalysisReport(userId, dateRange));
            report.setHealthAssessment(generateHealthAssessmentReport(userId, dateRange, report));

            return Result.success(report);
        } catch (Exception e) {
            return Result.error("生成健康报告失败: " + e.getMessage());
        }
    }

    @Override
    public Result<NutritionRecommendationResponse> calculateNutritionRecommendations(Long userId) {
        try {
            Result<UserResponse> userResult = userService.getUserById(userId);
            if (!userResult.getSuccess() || userResult.getData() == null) {
                return Result.error("获取用户信息失败");
            }

            UserResponse user = userResult.getData();
            
            // 获取用户基本信息，使用默认值如果缺失
            int age = user.getAge() != null ? user.getAge() : 25;
            double weight = user.getWeight() != null ? user.getWeight().doubleValue() : 70.0;
            double height = user.getHeight() != null ? user.getHeight().doubleValue() : 170.0;
            String gender = (user.getGender() != null && user.getGender() == 2) ? "female" : "male";
            
            // 使用Harris-Benedict公式计算基础代谢率(BMR)
            double bmr;
            if ("male".equals(gender)) {
                bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
            } else {
                bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
            }
            
            // 假设轻度活动水平，活动系数1.375
            double totalCalories = Math.round(bmr * 1.375);
            
            // 计算宏量营养素推荐量
            double protein = Math.round(weight * 1.6); // 每公斤体重1.6g蛋白质
            double fat = Math.round((totalCalories * 0.25) / 9); // 总热量的25%来自脂肪
            double carbohydrate = Math.round((totalCalories * 0.55) / 4); // 总热量的55%来自碳水
            double fiber = Math.round(age < 50 ? (gender.equals("male") ? 38 : 25) : (gender.equals("male") ? 30 : 21));
            
            NutritionRecommendationResponse response = new NutritionRecommendationResponse(
                totalCalories, protein, fat, carbohydrate, fiber
            );
            
            // 设置用户信息
            NutritionRecommendationResponse.UserBasicInfo userInfo = 
                new NutritionRecommendationResponse.UserBasicInfo(
                    age, gender, height, weight, bmr, "轻度活动"
                );
            response.setUserInfo(userInfo);
            
            return Result.success(response);
        } catch (Exception e) {
            return Result.error("计算营养推荐失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Object> getHistoricalComparison(Long userId, String period) {
        try {
            // 计算当前周期和上一周期的日期范围
            DateRange currentRange = calculateDateRange(period);
            DateRange previousRange = calculatePreviousDateRange(period);
            
            // 生成当前周期和上一周期的简化报告
            HealthReportResponse currentReport = generateSimplifiedReport(userId, currentRange);
            HealthReportResponse previousReport = generateSimplifiedReport(userId, previousRange);
            
            // 计算对比数据
            Map<String, Object> comparison = new HashMap<>();
            Map<String, Integer> previousPeriod = new HashMap<>();
            
            if (previousReport.getHealthAssessment() != null) {
                previousPeriod.put("overallScore", previousReport.getHealthAssessment().getOverallScore());
                previousPeriod.put("nutritionScore", previousReport.getHealthAssessment().getNutritionScore());
                previousPeriod.put("goalAchievementScore", previousReport.getHealthAssessment().getGoalAchievementScore());
                previousPeriod.put("consistencyScore", previousReport.getHealthAssessment().getConsistencyScore());
            } else {
                previousPeriod.put("overallScore", 0);
                previousPeriod.put("nutritionScore", 0);
                previousPeriod.put("goalAchievementScore", 0);
                previousPeriod.put("consistencyScore", 0);
            }
            
            int currentOverallScore = currentReport.getHealthAssessment() != null ? 
                currentReport.getHealthAssessment().getOverallScore() : 0;
            int improvement = currentOverallScore - previousPeriod.get("overallScore");
            
            comparison.put("previousPeriod", previousPeriod);
            comparison.put("improvement", improvement);
            
            return Result.success(comparison);
        } catch (Exception e) {
            return Result.error("获取历史对比数据失败: " + e.getMessage());
        }
    }
    
    // 私有辅助方法
    private DateRange calculateDateRange(String period) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate;
        
        switch (period) {
            case "week":
                startDate = endDate.minusWeeks(1);
                break;
            case "month":
                startDate = endDate.minusMonths(1);
                break;
            case "quarter":
                startDate = endDate.minusMonths(3);
                break;
            case "year":
                startDate = endDate.minusYears(1);
                break;
            default:
                startDate = endDate.minusWeeks(1);
        }
        
        return new DateRange(
            startDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
            endDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
        );
    }
    
    private DateRange calculatePreviousDateRange(String period) {
        DateRange currentRange = calculateDateRange(period);
        LocalDate currentStart = LocalDate.parse(currentRange.startDate);
        LocalDate currentEnd = LocalDate.parse(currentRange.endDate);
        
        long days = currentEnd.toEpochDay() - currentStart.toEpochDay();
        LocalDate previousEnd = currentStart.minusDays(1);
        LocalDate previousStart = previousEnd.minusDays(days);
        
        return new DateRange(
            previousStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
            previousEnd.format(DateTimeFormatter.ISO_LOCAL_DATE)
        );
    }
    
    private HealthReportResponse generateSimplifiedReport(Long userId, DateRange dateRange) {
        try {
            HealthReportResponse report = new HealthReportResponse();
            report.setStartDate(dateRange.startDate);
            report.setEndDate(dateRange.endDate);
            
            // 生成简化的报告数据
            report.setNutrition(generateNutritionReport(userId, dateRange));
            report.setGoalProgress(generateGoalProgressReport(userId, dateRange));
            report.setMealAnalysis(generateMealAnalysisReport(userId, dateRange));
            report.setHealthAssessment(generateHealthAssessmentReport(userId, dateRange, report));
            
            return report;
        } catch (Exception e) {
            // 返回空报告
            HealthReportResponse emptyReport = new HealthReportResponse();
            emptyReport.setStartDate(dateRange.startDate);
            emptyReport.setEndDate(dateRange.endDate);
            return emptyReport;
        }
    }
    
    // 生成营养报告
    private HealthReportResponse.NutritionReportData generateNutritionReport(Long userId, DateRange dateRange) {
        try {
            Result<MealRecordService.MealRecordStatistics> statsResult =
                mealRecordService.getMealRecordStatistics(userId,
                    LocalDate.parse(dateRange.startDate), LocalDate.parse(dateRange.endDate));

            if (!statsResult.getSuccess() || statsResult.getData() == null) {
                return new HealthReportResponse.NutritionReportData();
            }

            MealRecordService.MealRecordStatistics stats = statsResult.getData();
            HealthReportResponse.NutritionReportData nutrition = new HealthReportResponse.NutritionReportData();

            nutrition.setTotalCalories(stats.getTotalCalories().doubleValue());
            nutrition.setAverageCalories(stats.getAverageCalories().doubleValue());

            // 设置宏量营养素
            Map<String, Double> macronutrients = new HashMap<>();
            macronutrients.put("protein", stats.getTotalProtein().doubleValue());
            macronutrients.put("fat", stats.getTotalFat().doubleValue());
            macronutrients.put("carbohydrate", stats.getTotalCarbohydrate().doubleValue());
            macronutrients.put("fiber", stats.getTotalDietaryFiber().doubleValue());
            nutrition.setMacronutrients(macronutrients);

            // 计算宏量营养素百分比
            double totalMacros = stats.getTotalProtein().doubleValue() +
                               stats.getTotalFat().doubleValue() +
                               stats.getTotalCarbohydrate().doubleValue();

            Map<String, Double> percentages = new HashMap<>();
            if (totalMacros > 0) {
                percentages.put("protein", (stats.getTotalProtein().doubleValue() / totalMacros) * 100);
                percentages.put("fat", (stats.getTotalFat().doubleValue() / totalMacros) * 100);
                percentages.put("carbohydrate", (stats.getTotalCarbohydrate().doubleValue() / totalMacros) * 100);
            } else {
                percentages.put("protein", 0.0);
                percentages.put("fat", 0.0);
                percentages.put("carbohydrate", 0.0);
            }
            nutrition.setMacronutrientPercentages(percentages);

            // 获取营养推荐
            Result<NutritionRecommendationResponse> recommendationResult = calculateNutritionRecommendations(userId);
            if (recommendationResult.getSuccess() && recommendationResult.getData() != null) {
                nutrition.setRecommendations(recommendationResult.getData());

                // 计算合规评分
                double calorieCompliance = Math.min(stats.getAverageCalories().doubleValue() /
                    recommendationResult.getData().getCalories(), 1.0);
                nutrition.setComplianceScore((int) Math.round(calorieCompliance * 100));
            } else {
                nutrition.setComplianceScore(0);
            }

            return nutrition;
        } catch (Exception e) {
            return new HealthReportResponse.NutritionReportData();
        }
    }

    // 生成目标进度报告
    private HealthReportResponse.GoalProgressReportData generateGoalProgressReport(Long userId, DateRange dateRange) {
        try {
            HealthReportResponse.GoalProgressReportData goalProgress = new HealthReportResponse.GoalProgressReportData();

            // {{ AURA-X: Modify - 使用健康目标服务获取真实的用户目标数据. Approval: 寸止. }}
            try {
                Result<UserGoalStatsResponse> statsResult = healthGoalService.getUserGoalStats(userId);

                if (statsResult != null && statsResult.getSuccess() && statsResult.getData() != null) {
                    UserGoalStatsResponse stats = statsResult.getData();

                    // 使用真实的统计数据
                    goalProgress.setActiveGoals(stats.getActiveGoals() != null ? stats.getActiveGoals() : 0);
                    goalProgress.setCompletedGoals(stats.getCompletedGoals() != null ? stats.getCompletedGoals() : 0);
                    goalProgress.setTotalGoals(stats.getTotalGoals() != null ? stats.getTotalGoals() : 0);
                    goalProgress.setOverallProgress(stats.getOverallProgress() != null ? stats.getOverallProgress() : 0.0);
                    goalProgress.setCurrentStreak(stats.getCurrentStreak() != null ? stats.getCurrentStreak() : 0);
                } else {
                    // 如果获取统计数据失败，使用默认值
                    goalProgress.setActiveGoals(0);
                    goalProgress.setCompletedGoals(0);
                    goalProgress.setTotalGoals(0);
                    goalProgress.setOverallProgress(0.0);
                    goalProgress.setCurrentStreak(0);
                }
            } catch (Exception statsEx) {
                // 使用默认值
                goalProgress.setActiveGoals(0);
                goalProgress.setCompletedGoals(0);
                goalProgress.setTotalGoals(0);
                goalProgress.setOverallProgress(0.0);
                goalProgress.setCurrentStreak(0);
            }

            // 获取用户目标详情
            try {
                Result<List<UserGoalResponse>> goalsResult = healthGoalService.getUserGoals(userId);

                if (goalsResult != null && goalsResult.getSuccess() && goalsResult.getData() != null) {
                    List<HealthReportResponse.GoalDetailData> goalDetails = new ArrayList<>();

                    for (UserGoalResponse goal : goalsResult.getData()) {
                        // 只包含活跃的目标
                        if (goal.getIsActive() != null && goal.getIsActive()) {
                            HealthReportResponse.GoalDetailData detail = new HealthReportResponse.GoalDetailData();
                            detail.setGoalId(goal.getId());
                            detail.setGoalName(goal.getGoalName());
                            detail.setGoalType(goal.getGoalType());
                            detail.setTargetValue(goal.getTargetValue());
                            detail.setCurrentValue(goal.getCurrentValue());
                            detail.setProgress(goal.getProgress());
                            detail.setIsCompleted(goal.getIsCompleted());
                            detail.setTrend("stable"); // 默认趋势，可以后续根据历史数据计算
                            goalDetails.add(detail);
                        }
                    }

                    goalProgress.setGoalDetails(goalDetails);
                } else {
                    goalProgress.setGoalDetails(new ArrayList<>());
                }
            } catch (Exception goalsEx) {
                goalProgress.setGoalDetails(new ArrayList<>());
            }

            return goalProgress;
        } catch (Exception e) {
            // 发生异常时返回空的目标进度报告
            HealthReportResponse.GoalProgressReportData emptyProgress = new HealthReportResponse.GoalProgressReportData();
            emptyProgress.setActiveGoals(0);
            emptyProgress.setCompletedGoals(0);
            emptyProgress.setTotalGoals(0);
            emptyProgress.setOverallProgress(0.0);
            emptyProgress.setCurrentStreak(0);
            emptyProgress.setGoalDetails(new ArrayList<>());
            return emptyProgress;
        }
    }

    // 生成膳食分析报告
    private HealthReportResponse.MealAnalysisReportData generateMealAnalysisReport(Long userId, DateRange dateRange) {
        try {
            Result<MealRecordService.MealRecordStatistics> statsResult =
                mealRecordService.getMealRecordStatistics(userId,
                    LocalDate.parse(dateRange.startDate), LocalDate.parse(dateRange.endDate));

            if (!statsResult.getSuccess() || statsResult.getData() == null) {
                return new HealthReportResponse.MealAnalysisReportData();
            }

            MealRecordService.MealRecordStatistics stats = statsResult.getData();
            HealthReportResponse.MealAnalysisReportData mealAnalysis = new HealthReportResponse.MealAnalysisReportData();

            mealAnalysis.setTotalMeals(stats.getTotalRecords());
            mealAnalysis.setAverageMealsPerDay((double) stats.getTotalRecords() / Math.max(stats.getTotalDays(), 1));

            // 设置膳食类型分布
            Map<String, Integer> distribution = new HashMap<>();
            distribution.put("breakfast", stats.getMealTypeDistribution().getBreakfast());
            distribution.put("lunch", stats.getMealTypeDistribution().getLunch());
            distribution.put("dinner", stats.getMealTypeDistribution().getDinner());
            distribution.put("snack", stats.getMealTypeDistribution().getSnack());
            mealAnalysis.setMealTypeDistribution(distribution);

            // 计算饮食模式评分
            Map<String, Integer> patterns = new HashMap<>();
            int regularityScore = stats.getTotalDays() > 0 ?
                Math.round((float) stats.getTotalRecords() / (stats.getTotalDays() * 3) * 100) : 0;
            patterns.put("regularityScore", Math.min(regularityScore, 100));
            patterns.put("balanceScore", 75); // 简化评分
            patterns.put("varietyScore", 70);  // 简化评分
            mealAnalysis.setEatingPatterns(patterns);

            mealAnalysis.setPopularFoods(new ArrayList<>());

            return mealAnalysis;
        } catch (Exception e) {
            return new HealthReportResponse.MealAnalysisReportData();
        }
    }

    // 生成健康评估报告
    private HealthReportResponse.HealthAssessmentReportData generateHealthAssessmentReport(
            Long userId, DateRange dateRange, HealthReportResponse report) {
        try {
            HealthReportResponse.HealthAssessmentReportData assessment = new HealthReportResponse.HealthAssessmentReportData();

            // 计算各项评分
            int nutritionScore = report.getNutrition() != null ?
                report.getNutrition().getComplianceScore() : 0;
            int goalScore = report.getGoalProgress() != null ?
                report.getGoalProgress().getOverallProgress().intValue() : 0;
            int consistencyScore = report.getMealAnalysis() != null &&
                report.getMealAnalysis().getEatingPatterns() != null ?
                report.getMealAnalysis().getEatingPatterns().get("regularityScore") : 0;

            int overallScore = Math.round((nutritionScore + goalScore + consistencyScore) / 3.0f);

            assessment.setOverallScore(overallScore);
            assessment.setNutritionScore(nutritionScore);
            assessment.setGoalAchievementScore(goalScore);
            assessment.setConsistencyScore(consistencyScore);

            // 生成建议和优势
            List<String> improvements = new ArrayList<>();
            List<String> strengths = new ArrayList<>();
            List<String> recommendations = new ArrayList<>();

            if (nutritionScore < 70) {
                improvements.add("营养摄入均衡性");
                recommendations.add("建议增加蛋白质和膳食纤维的摄入，控制总热量");
            } else {
                strengths.add("营养摄入较为均衡");
            }

            if (goalScore < 70) {
                improvements.add("健康目标达成率");
                recommendations.add("建议设定更具体可行的目标，并保持每日记录");
            } else {
                strengths.add("健康目标执行良好");
            }

            if (consistencyScore < 70) {
                improvements.add("饮食规律性");
                recommendations.add("建议保持规律的用餐时间，增加食物种类多样性");
            } else {
                strengths.add("饮食习惯规律");
            }

            assessment.setImprovementAreas(improvements);
            assessment.setStrengths(strengths);
            assessment.setRecommendations(recommendations);

            // 设置趋势
            Map<String, String> trends = new HashMap<>();
            trends.put("nutrition", "stable");
            trends.put("goals", goalScore > 50 ? "improving" : "stable");
            trends.put("consistency", consistencyScore > 70 ? "stable" : "declining");
            assessment.setTrends(trends);

            return assessment;
        } catch (Exception e) {
            return new HealthReportResponse.HealthAssessmentReportData();
        }
    }

    // 内部数据类
    private static class DateRange {
        final String startDate;
        final String endDate;

        DateRange(String startDate, String endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }
}
