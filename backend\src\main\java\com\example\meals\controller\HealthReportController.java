package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.service.HealthReportService;
import com.example.meals.dto.HealthReportResponse;
import com.example.meals.dto.NutritionRecommendationResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 健康报告控制器
 * {{ AURA-X: Add - 创建健康报告控制器，提供报告生成API. Approval: 寸止. }}
 */
@RestController
@RequestMapping("/api/health-reports")
public class HealthReportController {
    
    @Autowired
    private HealthReportService healthReportService;
    
    /**
     * 生成健康报告
     * @param period 报告时间范围 (week/month/quarter/year)
     * @param request HTTP请求
     * @return 健康报告数据
     */
    @GetMapping("/generate")
    public Result<HealthReportResponse> generateHealthReport(
            @RequestParam(defaultValue = "week") String period,
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }
        
        return healthReportService.generateHealthReport(userId, period);
    }
    
    /**
     * 获取用户营养推荐摄入量
     * @param request HTTP请求
     * @return 营养推荐数据
     */
    @GetMapping("/nutrition-recommendations")
    public Result<NutritionRecommendationResponse> getNutritionRecommendations(
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }
        
        return healthReportService.calculateNutritionRecommendations(userId);
    }
    
    /**
     * 获取历史报告对比数据
     * @param period 当前报告时间范围
     * @param request HTTP请求
     * @return 历史对比数据
     */
    @GetMapping("/comparison")
    public Result<Object> getHistoricalComparison(
            @RequestParam(defaultValue = "week") String period,
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }
        
        return healthReportService.getHistoricalComparison(userId, period);
    }
}
