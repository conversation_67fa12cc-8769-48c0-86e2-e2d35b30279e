<template>
  <div class="nutrition-container">
    <!-- 顶部导航栏 -->
    <TopNavbar
      page-title="营养分析"
      back-to="/dashboard"
      back-text="返回控制台"
      :show-user-info="false"
    />

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 页面标题区域 -->
      <section class="page-header">
        <div class="header-content">
          <h1 class="page-title">营养分析</h1>
          <p class="page-subtitle">分析食物营养成分，制定健康饮食计划</p>
        </div>
      </section>

      <!-- 食物搜索区域 -->
      <section class="search-section">
        <div class="search-card">
          <h2 class="section-title">
            <span class="title-icon">🔍</span>
            食物搜索
          </h2>
          <div class="search-container">
            <div class="search-input-wrapper">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索食物名称，如：苹果、鸡蛋、牛奶..."
                class="search-input"
                @input="handleSearch"
                @keyup.enter="performSearch"
              />
              <button @click="performSearch" class="search-btn" :disabled="!searchQuery.trim() || isSearching">
                {{ isSearching ? '搜索中...' : '搜索' }}
              </button>
            </div>

            <!-- 搜索错误提示 -->
            <div v-if="searchError" class="search-error">
              <div class="error-icon">⚠️</div>
              <span class="error-message">{{ searchError }}</span>
            </div>

            <!-- 搜索建议 -->
            <div v-if="searchSuggestions.length > 0" class="search-suggestions">
              <!-- 搜索结果提示 -->
              <div v-if="searchSuggestions.length > 5" class="suggestions-header">
                <span class="suggestions-count">找到 {{ searchSuggestions.length }} 个结果，可滚动查看更多</span>
              </div>

              <div
                v-for="suggestion in searchSuggestions"
                :key="suggestion.id"
                class="suggestion-item"
                @click="selectFood(suggestion)"
              >
                <span class="food-name">{{ suggestion.foodName }}</span>
                <span class="food-category">{{ suggestion.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 选中的食物信息 -->
      <section v-if="selectedFood" class="food-info-section">
        <div class="food-info-card">
          <div class="food-header">
            <h2 class="section-title">
              <span class="title-icon">🥗</span>
              {{ selectedFood.foodName }}
            </h2>
            <span class="food-category-badge">种类：{{ selectedFood.category }}</span>
          </div>

          <!-- 重量输入 -->
          <div class="weight-input-section">
            <label class="weight-label">食用重量：</label>
            <div class="weight-input-wrapper">
              <input
                v-model.number="foodWeight"
                type="number"
                min="1"
                max="10000"
                class="weight-input"
                @input="validateAndCalculateNutrition"
              />
              <span class="weight-unit">克</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门食物推荐 -->
      <section v-if="!selectedFood && popularFoods.length > 0" class="popular-section">
        <div class="popular-card">
          <h2 class="section-title">
            <span class="title-icon">🔥</span>
            热门食物推荐
          </h2>
          <div class="popular-carousel">
            <Transition name="slide-fade" mode="out-in">
              <div class="popular-grid" :key="currentSlide">
                <div
                  v-for="food in currentPageFoods"
                  :key="food.id"
                  class="popular-item"
                  @click="selectFood(food)"
                >
                  <div class="popular-name">{{ food.foodName }}</div>
                  <div class="popular-category">{{ food.category }}</div>
                  <div class="popular-calories">{{ food.energyKcal }}千卡/100g</div>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </section>

      <!-- 营养信息展示区域 -->
      <section v-if="selectedFood" class="nutrition-section">
        <div class="nutrition-card">
          <div class="nutrition-header">
            <h2 class="section-title">
              <span class="title-icon">📊</span>
              营养成分分析
            </h2>
            <button @click="clearSelection" class="clear-btn">重新选择</button>
          </div>
          <p class="nutrition-subtitle">基于 {{ foodWeight }}g {{ selectedFood.foodName }} 的营养成分</p>

          <!-- 加载状态 -->
          <div v-if="isAnalyzing" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在分析营养成分...</p>
          </div>

          <!-- 错误状态 -->
          <div v-if="nutritionError" class="error-state">
            <div class="error-icon">⚠️</div>
            <div class="error-content">
              <h4>营养分析失败</h4>
              <p>{{ nutritionError }}</p>
              <button @click="retryNutritionAnalysis" class="retry-btn">重试</button>
            </div>
          </div>

          <!-- 主要营养素 -->
          <div v-if="nutritionAnalysis && !nutritionError" class="main-nutrients">
            <div class="nutrient-item primary">
              <div class="nutrient-icon">🔥</div>
              <div class="nutrient-info">
                <span class="nutrient-name">热量</span>
                <span class="nutrient-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.calories, '') }}</span>
                <span class="nutrient-unit">千卡</span>
              </div>
            </div>

            <div class="nutrient-item primary">
              <div class="nutrient-icon">🥩</div>
              <div class="nutrient-info">
                <span class="nutrient-name">蛋白质</span>
                <span class="nutrient-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.protein, 'g') }}</span>
                <span class="nutrient-unit">g</span>
              </div>
            </div>

            <div class="nutrient-item primary">
              <div class="nutrient-icon">🧈</div>
              <div class="nutrient-info">
                <span class="nutrient-name">脂肪</span>
                <span class="nutrient-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.fat, 'g') }}</span>
                <span class="nutrient-unit">g</span>
              </div>
            </div>

            <div class="nutrient-item primary">
              <div class="nutrient-icon">🍞</div>
              <div class="nutrient-info">
                <span class="nutrient-name">碳水化合物</span>
                <span class="nutrient-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.carbohydrates, 'g') }}</span>
                <span class="nutrient-unit">g</span>
              </div>
            </div>
          </div>

          <!-- 详细营养素 -->
          <div v-if="nutritionAnalysis && !nutritionError" class="detailed-nutrients">
            <h3 class="detailed-title">详细营养成分</h3>

            <!-- 基础营养成分 -->
            <div class="nutrient-category">
              <h4 class="category-title">基础营养</h4>
              <div class="nutrient-grid">
                <div class="nutrient-detail">
                  <span class="detail-name">膳食纤维</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.fiber, 'g') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">胆固醇</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.cholesterol, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">水分</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.water, 'g') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">灰分</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.ash, 'g') }}</span>
                </div>
              </div>
            </div>

            <!-- 维生素类 -->
            <div class="nutrient-category">
              <h4 class="category-title">维生素</h4>
              <div class="nutrient-grid">
                <div class="nutrient-detail">
                  <span class="detail-name">维生素A</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.vitaminA, 'μg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">维生素C</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.vitaminC, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">维生素E</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.vitaminE, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">硫胺素(B1)</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.thiamin, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">核黄素(B2)</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.riboflavin, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">烟酸</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.niacin, 'mg') }}</span>
                </div>
              </div>
            </div>

            <!-- 矿物质类 -->
            <div class="nutrient-category">
              <h4 class="category-title">矿物质</h4>
              <div class="nutrient-grid">
                <div class="nutrient-detail">
                  <span class="detail-name">钙</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.calcium, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">铁</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.iron, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">锌</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.zinc, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">硒</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.selenium, 'μg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">磷</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.phosphorus, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">钾</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.potassium, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">钠</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.calculatedNutrition.sodium, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">镁</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.magnesium, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">铜</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.copper, 'mg') }}</span>
                </div>
                <div class="nutrient-detail">
                  <span class="detail-name">锰</span>
                  <span class="detail-value">{{ formatNutritionValue(nutritionAnalysis.detailedNutrients.manganese, 'mg') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 每日营养建议 -->
      <section class="recommendation-section">
        <div class="recommendation-card">
          <h2 class="section-title">
            <span class="title-icon">💡</span>
            每日营养建议
          </h2>
          <div class="recommendation-grid">
            <div class="recommendation-item">
              <div class="rec-icon">🔥</div>
              <div class="rec-content">
                <h4>成人每日热量需求</h4>
                <p>男性：2000-2500千卡<br>女性：1600-2000千卡</p>
              </div>
            </div>
            <div class="recommendation-item">
              <div class="rec-icon">🥩</div>
              <div class="rec-content">
                <h4>蛋白质建议摄入</h4>
                <p>每公斤体重需要0.8-1.2g蛋白质</p>
              </div>
            </div>
            <div class="recommendation-item">
              <div class="rec-icon">🥬</div>
              <div class="rec-content">
                <h4>膳食纤维建议</h4>
                <p>成人每日需要25-35g膳食纤维</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import TopNavbar from '../components/TopNavbar.vue'
import {
  searchFoods,
  analyzeNutrition,
  getAllCategories,
  getPopularFoods,
  type FoodItem,
  type NutritionAnalysis,
  formatNutritionValue
} from '../utils/foodApi'

// 响应式数据
const searchQuery = ref('')
const searchSuggestions = ref<FoodItem[]>([])
const selectedFood = ref<FoodItem | null>(null)
const foodWeight = ref(100) // 默认100克
const isSearching = ref(false)
const isAnalyzing = ref(false)
const categories = ref<string[]>([])
const popularFoods = ref<FoodItem[]>([])
const nutritionAnalysis = ref<NutritionAnalysis | null>(null)
const nutritionError = ref('')
const searchError = ref('')

// {{ AURA-X: Add - 添加轮播相关的响应式数据. Approval: 寸止. }}
const currentSlide = ref(0)
const itemsPerPage = 5
const autoSlideTimer = ref<number | null>(null)

// {{ AURA-X: Add - 添加轮播相关的计算属性. Approval: 寸止. }}
// 计算属性
const totalSlides = computed(() => Math.ceil(popularFoods.value.length / itemsPerPage))
const currentPageFoods = computed(() => {
  const start = currentSlide.value * itemsPerPage
  const end = start + itemsPerPage
  return popularFoods.value.slice(start, end)
})

// 输入验证函数
const validateSearchQuery = (query: string): boolean => {
  // 过滤特殊字符，只允许中文、英文、数字、空格、括号
  const validPattern = /^[\u4e00-\u9fa5a-zA-Z0-9\s()（）\[\]【】]+$/
  // {{ AURA-X: Modify - 支持单字查询，将最小长度从2改为1. Approval: 寸止. }}
  return query.length >= 1 && query.length <= 50 && validPattern.test(query)
}

const validateWeight = (weight: number): boolean => {
  return weight >= 1 && weight <= 10000 && !isNaN(weight)
}

// 搜索处理函数
const handleSearch = async () => {
  const query = searchQuery.value.trim()

  // {{ AURA-X: Modify - 支持单字查询，将最小长度检查从2改为1. Approval: 寸止. }}
  if (query.length < 1) {
    searchSuggestions.value = []
    searchError.value = ''
    return
  }

  if (!validateSearchQuery(query)) {
    // {{ AURA-X: Modify - 更新错误提示信息，支持1-50个字符. Approval: 寸止. }}
    searchError.value = '搜索关键词格式不正确，请输入1-50个字符的中文、英文或数字'
    searchSuggestions.value = []
    return
  }

  try {
    isSearching.value = true
    searchError.value = ''
    const foods = await searchFoods(query, 100)
    searchSuggestions.value = foods
  } catch (error) {
    console.error('搜索出错:', error)
    searchError.value = error instanceof Error ? error.message : '搜索失败，请检查网络连接'
    searchSuggestions.value = []
  } finally {
    isSearching.value = false
  }
}

const performSearch = () => {
  if (!searchQuery.value.trim()) return
  handleSearch()
}

const selectFood = async (food: FoodItem) => {
  selectedFood.value = food
  searchQuery.value = food.foodName
  searchSuggestions.value = []
  await calculateNutrition()
}

// 重量验证和营养计算
const validateAndCalculateNutrition = () => {
  if (!validateWeight(foodWeight.value)) {
    nutritionError.value = '重量必须是1-10000之间的数值'
    return
  }
  calculateNutrition()
}

const calculateNutrition = async () => {
  if (!selectedFood.value) return

  try {
    isAnalyzing.value = true
    nutritionError.value = ''
    const analysis = await analyzeNutrition(selectedFood.value.id, foodWeight.value)
    nutritionAnalysis.value = analysis
  } catch (error) {
    console.error('营养分析出错:', error)
    nutritionError.value = error instanceof Error ? error.message : '营养分析失败，请重试'
    nutritionAnalysis.value = null
  } finally {
    isAnalyzing.value = false
  }
}

const retryNutritionAnalysis = () => {
  nutritionError.value = ''
  calculateNutrition()
}

const clearSelection = () => {
  selectedFood.value = null
  nutritionAnalysis.value = null
  nutritionError.value = ''
  searchQuery.value = ''
  searchSuggestions.value = []
  searchError.value = ''
  foodWeight.value = 100
}

// {{ AURA-X: Remove - 移除手动控制方法，只保留自动轮播. Approval: 寸止. }}

// {{ AURA-X: Add - 添加自动轮播功能. Approval: 寸止. }}
// 自动轮播功能
const startAutoSlide = () => {
  if (totalSlides.value <= 1) return

  autoSlideTimer.value = window.setInterval(() => {
    if (currentSlide.value < totalSlides.value - 1) {
      currentSlide.value++
    } else {
      currentSlide.value = 0 // 循环到第一页
    }
  }, 3000) // 每3秒切换
}

const stopAutoSlide = () => {
  if (autoSlideTimer.value) {
    clearInterval(autoSlideTimer.value)
    autoSlideTimer.value = null
  }
}

// {{ AURA-X: Remove - 移除重启方法，纯自动轮播无需用户操作重启. Approval: 寸止. }}

// 初始化数据
const initializeData = async () => {
  try {
    // 获取食物分类
    const categoriesData = await getAllCategories()
    categories.value = categoriesData

    // {{ AURA-X: Modify - 增加获取热门食物数量以支持轮播. Approval: 寸止. }}
    // 获取热门食物
    const popularData = await getPopularFoods(15) // 增加到15个以支持轮播
    popularFoods.value = popularData
  } catch (error) {
    console.error('初始化数据失败:', error)
    // 初始化失败不影响主要功能，只记录错误
  }
}

onMounted(async () => {
  await initializeData()
  // {{ AURA-X: Add - 数据加载完成后启动自动轮播. Approval: 寸止. }}
  // 延迟启动自动轮播，确保用户有时间查看
  setTimeout(startAutoSlide, 2000)
})

// {{ AURA-X: Add - 组件卸载时清理定时器. Approval: 寸止. }}
onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
/* NutritionView 特定样式 - 使用全局CSS变量 */

/* 全局样式 */
.nutrition-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 页面标题区域 */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.header-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  /* 移除渐变文字效果，使用普通颜色确保可见性 */
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

/* 通用卡片样式 */
.search-card,
.food-info-card,
.nutrition-card,
.recommendation-card,
.popular-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  position: relative;
}

/* 搜索卡片特殊层级 */
.search-card {
  z-index: 1000;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.title-icon {
  font-size: 1.25rem;
}

/* 搜索区域样式 */
.search-container {
  position: relative;
  z-index: 1001;
}

.search-input-wrapper {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #374151;
}

.search-input:focus {
  outline: none;
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-btn {
  padding: 0.75rem 1.5rem;
  background: #16a085;
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.search-btn:hover:not(:disabled) {
  background: #138d75;
  transform: translateY(-1px);
}

.search-btn:disabled {
  background: #bdc3c7;
  color: #7f8c8d;
  cursor: not-allowed;
}

/* 搜索建议样式 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 320px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) #f1f1f1;
}

/* 自定义滚动条样式 */
.search-suggestions::-webkit-scrollbar {
  width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* 搜索错误提示样式 */
.search-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: var(--radius-md);
  color: #c53030;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.search-error .error-icon {
  font-size: 1rem;
}

.search-error .error-message {
  flex: 1;
}

/* 搜索建议头部和提示样式 */
.suggestions-header {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: center;
}

.suggestions-count {
  font-weight: 500;
}



.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-color);
  background: #ffffff;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: #f8f9fa;
  transform: translateX(2px);
}

.food-name {
  font-weight: 500;
  color: var(--text-primary);
}

.food-category {
  font-size: 0.875rem;
  color: var(--text-secondary);
  background: var(--background-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

/* 热门食物推荐样式 */
/* {{ AURA-X: Fix - 简化轮播容器样式，移除transform相关. Approval: 寸止. }} */
.popular-carousel {
  position: relative;
}

.popular-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 固定显示5列 */
  gap: 1rem;
}

.popular-item {
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 0; /* 防止内容溢出 */
}

.popular-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
  border-color: var(--primary-color);
}

.popular-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.popular-category {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.popular-calories {
  font-size: 0.875rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* {{ AURA-X: Add - 添加轮播过渡动画样式. Approval: 寸止. }} */
/* 轮播过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* {{ AURA-X: Modify - 简化响应式设计，移除控制元素样式. Approval: 寸止. }} */
/* 响应式设计 */
@media (max-width: 1200px) {
  .popular-grid {
    grid-template-columns: repeat(4, 1fr); /* 中等屏幕显示4列 */
  }
}

@media (max-width: 900px) {
  .popular-grid {
    grid-template-columns: repeat(3, 1fr); /* 小屏幕显示3列 */
  }
}

@media (max-width: 600px) {
  .popular-grid {
    grid-template-columns: repeat(2, 1fr); /* 手机屏幕显示2列 */
  }

  .popular-item {
    padding: 0.75rem;
  }
}

/* 食物信息样式 */
.food-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.food-category-badge {
  background: var(--primary-color);
  color: rgb(12, 12, 12);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
}

.weight-input-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
}

.weight-label {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

.weight-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.weight-input {
  width: 100px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  text-align: center;
  font-weight: 500;
}

.weight-unit {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 营养信息样式 */
.nutrition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.clear-btn {
  padding: 0.5rem 1rem;
  background: var(--text-light);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #16a085;
  color: white;
}

.nutrition-subtitle {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-style: italic;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-state {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: var(--radius-lg);
  margin: 2rem 0;
}

.error-icon {
  font-size: 2rem;
  color: #c53030;
}

.error-content {
  flex: 1;
}

.error-content h4 {
  color: #c53030;
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.error-content p {
  color: #742a2a;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.retry-btn {
  padding: 0.5rem 1rem;
  background: #c53030;
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #9c2626;
  transform: translateY(-1px);
}

.main-nutrients {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.nutrient-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.nutrient-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.nutrient-item.primary {
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
  color: white !important;
}

.nutrient-item.primary .nutrient-value,
.nutrient-item.primary .nutrient-name,
.nutrient-item.primary .nutrient-unit,
.nutrient-item.primary .nutrient-icon {
  color: white !important;
}

.nutrient-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.nutrient-item.primary .nutrient-icon {
  background: rgba(255, 255, 255, 0.3);
}

.nutrient-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nutrient-name {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.9;
}

.nutrient-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.nutrient-unit {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* 详细营养成分样式 */
.detailed-nutrients {
  border-top: 1px solid var(--border-color);
  padding-top: 2rem;
}

.detailed-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

/* 营养成分分类样式 */
.nutrient-category {
  margin-bottom: 2rem;
}

.category-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
  display: inline-block;
}

.nutrient-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.nutrient-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.nutrient-detail:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(22, 160, 133, 0.1);
  transform: translateY(-1px);
}

.detail-name {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

/* 推荐区域样式 */
.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.rec-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
  flex-shrink: 0;
}

.rec-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.rec-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .search-card,
  .food-info-card,
  .nutrition-card,
  .recommendation-card {
    padding: 1.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .main-nutrients {
    grid-template-columns: 1fr;
  }

  .nutrient-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .recommendation-grid {
    grid-template-columns: 1fr;
  }

  .search-input-wrapper {
    flex-direction: column;
  }

  .weight-input-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .nutrient-grid {
    grid-template-columns: 1fr;
  }

  .food-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>
